<?php
/**
 * 身份证库补齐API
 * 根据姓名和部分身份证号补齐完整身份证信息
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $name = $_GET['name'] ?? '';
        $idcard = $_GET['idcard'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $name = $input['name'] ?? '';
        $idcard = $input['idcard'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($name)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入姓名'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($idcard)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入部分身份证号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证姓名格式（2-4个中文字符）
    if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $name)) {
        echo json_encode([
            'code' => 400,
            'message' => '姓名格式不正确，请输入2-4个中文字符'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证身份证号格式（允许部分号码，包含多个x或X）
    // 检查身份证号长度
    $idcardLength = strlen($idcard);
    if ($idcardLength < 6 || $idcardLength > 18) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号长度不正确，请输入6-18位的部分身份证号'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查首位是否为1-9
    $firstChar = substr($idcard, 0, 1);
    if (!in_array($firstChar, ['1', '2', '3', '4', '5', '6', '7', '8', '9'])) {
        echo json_encode([
            'code' => 400,
            'message' => '身份证号首位必须是1-9'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查是否只包含允许的字符（数字、x、X）
    for ($i = 0; $i < $idcardLength; $i++) {
        $char = substr($idcard, $i, 1);
        if (!in_array(strtolower($char), ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'x'])) {
            echo json_encode([
                'code' => 400,
                'message' => '身份证号只能包含数字和字母x（支持用x代替未知数字）'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'http://api.qnm6.top/api/kb/';
    $params = [
        'token' => $token,
        'name' => $name,
        'idcard' => $idcard
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用cURL进行更可靠的HTTP请求
    $response = makeHttpRequest($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '身份证库补齐服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '身份证库补齐结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，解析msg字段中的结构化数据
    if (isset($result['msg']) && !empty($result['msg'])) {
        $parsedData = parseIdCardData($result['msg']);
        $result['parsed_data'] = $parsedData;
        $result['total_records'] = count($parsedData);
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 解析身份证补齐数据
 */
function parseIdCardData($msg) {
    $records = [];
    
    // 按双换行分割记录
    $sections = explode("\n\n", $msg);
    
    foreach ($sections as $section) {
        if (empty(trim($section))) continue;
        
        $record = [];
        $lines = explode("\n", $section);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // 解析Name和IdCard字段
            if (strpos($line, 'Name:') !== false) {
                $record['name'] = trim(str_replace('Name:', '', $line));
            } elseif (strpos($line, 'IdCard:') !== false) {
                $record['idcard'] = trim(str_replace('IdCard:', '', $line));
            }
        }
        
        // 只添加有效记录
        if (!empty($record['name']) && !empty($record['idcard'])) {
            $records[] = $record;
        }
    }
    
    return $records;
}

/**
 * 使用cURL进行HTTP请求，支持重试机制
 */
function makeHttpRequest($url, $maxRetries = 3) {
    $retryCount = 0;
    
    while ($retryCount < $maxRetries) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'iDatas-IdCardComplete/1.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        // 请求成功
        if ($response !== false && $httpCode == 200) {
            return $response;
        }
        
        $retryCount++;
        
        // 如果不是最后一次重试，等待一段时间再重试
        if ($retryCount < $maxRetries) {
            usleep(500000); // 等待0.5秒
        }
    }
    
    return false;
}
?>
