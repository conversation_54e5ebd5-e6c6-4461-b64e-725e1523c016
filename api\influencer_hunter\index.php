<?php
/**
 * 网红信息猎魔API
 * 查询网红相关信息
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $msg = $_GET['msg'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $msg = $input['msg'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($msg)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入查询关键词'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证查询关键词格式
    $msg = trim($msg);
    if (strlen($msg) < 1) {
        echo json_encode([
            'code' => 400,
            'message' => '查询关键词不能为空'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/api/wh/';
    $params = [
        'token' => $token,
        'msg' => $msg
    ];
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用简单的file_get_contents进行HTTP请求
    $response = @file_get_contents($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '网红信息猎魔服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '网红信息猎魔结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，解析shuju字段中的结构化数据
    if (isset($result['shuju']) && !empty($result['shuju'])) {
        $parsedData = parseInfluencerData($result['shuju']);
        $result['parsed_data'] = $parsedData;
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 解析网红信息数据
 */
function parseInfluencerData($shuju) {
    $data = [
        'influencers' => [],
        'summary' => []
    ];
    
    // 按双换行分割不同的网红信息
    $sections = explode("\n\n", $shuju);
    
    foreach ($sections as $section) {
        $section = trim($section);
        if (empty($section)) continue;
        
        $influencer = parseInfluencerSection($section);
        if (!empty($influencer)) {
            $data['influencers'][] = $influencer;
        }
    }
    
    // 生成摘要信息
    $data['summary'] = [
        'total_count' => count($data['influencers']),
        'platforms' => [],
        'regions' => []
    ];
    
    // 统计平台和地区信息
    foreach ($data['influencers'] as $influencer) {
        if (!empty($influencer['platform']) && !in_array($influencer['platform'], $data['summary']['platforms'])) {
            $data['summary']['platforms'][] = $influencer['platform'];
        }
        if (!empty($influencer['region']) && !in_array($influencer['region'], $data['summary']['regions'])) {
            $data['summary']['regions'][] = $influencer['region'];
        }
    }
    
    return $data;
}

/**
 * 解析单个网红信息段落
 */
function parseInfluencerSection($section) {
    $influencer = [
        'raw_text' => $section,
        'nickname' => '',
        'real_name' => '',
        'idcard' => '',
        'phone' => '',
        'qq' => '',
        'weibo' => '',
        'address' => '',
        'platform' => '',
        'region' => ''
    ];
    
    // 尝试解析各种格式的信息
    $lines = explode("\n", $section);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // 解析不同格式的信息
        if (preg_match('/平台🆔(.+?)\s+姓名[:：](.+?)\s+身份证[:：]\s*(\d+[xX]?)/u', $line, $matches)) {
            $influencer['platform'] = '平台';
            $influencer['nickname'] = trim($matches[1]);
            $influencer['real_name'] = trim($matches[2]);
            $influencer['idcard'] = trim($matches[3]);
        } elseif (preg_match('/(.+?)\s+(.+?)\s+(\d{15,18}[xX]?)/u', $line, $matches)) {
            // 格式：昵称 真名 身份证
            $influencer['nickname'] = trim($matches[1]);
            $influencer['real_name'] = trim($matches[2]);
            $influencer['idcard'] = trim($matches[3]);
        } elseif (preg_match('/姓名[:：](.+?)(?:\s|$)/u', $line, $matches)) {
            $influencer['real_name'] = trim($matches[1]);
        } elseif (preg_match('/身份证[:：]\s*(\d+[xX]?)/u', $line, $matches)) {
            $influencer['idcard'] = trim($matches[1]);
        } elseif (preg_match('/电话[:：](\d+)/u', $line, $matches)) {
            $influencer['phone'] = trim($matches[1]);
        } elseif (preg_match('/QQ号.*[:：](\d+)/u', $line, $matches)) {
            $influencer['qq'] = trim($matches[1]);
        } elseif (preg_match('/微博链接地址.*[:：](https?:\/\/[^\s]+)/u', $line, $matches)) {
            $influencer['weibo'] = trim($matches[1]);
        } elseif (preg_match('/户籍地址[:：](.+?)(?:\s|$)/u', $line, $matches)) {
            $influencer['address'] = trim($matches[1]);
        }
    }
    
    // 如果没有解析到昵称，尝试从第一行提取
    if (empty($influencer['nickname']) && !empty($lines[0])) {
        $firstLine = trim($lines[0]);
        if (!empty($firstLine) && !preg_match('/[:：]/', $firstLine)) {
            $parts = preg_split('/\s+/', $firstLine);
            if (!empty($parts[0])) {
                $influencer['nickname'] = $parts[0];
            }
        }
    }
    
    // 从身份证号提取地区信息
    if (!empty($influencer['idcard']) && strlen($influencer['idcard']) >= 6) {
        $regionCode = substr($influencer['idcard'], 0, 6);
        $influencer['region'] = getRegionByCode($regionCode);
    }
    
    // 只有包含有效信息才返回
    if (!empty($influencer['nickname']) || !empty($influencer['real_name']) || !empty($influencer['idcard'])) {
        return $influencer;
    }
    
    return null;
}

/**
 * 根据身份证前6位获取地区信息（简化版本）
 */
function getRegionByCode($code) {
    $regions = [
        '110' => '北京市',
        '120' => '天津市',
        '130' => '河北省',
        '140' => '山西省',
        '150' => '内蒙古',
        '210' => '辽宁省',
        '220' => '吉林省',
        '230' => '黑龙江省',
        '310' => '上海市',
        '320' => '江苏省',
        '330' => '浙江省',
        '340' => '安徽省',
        '350' => '福建省',
        '360' => '江西省',
        '370' => '山东省',
        '410' => '河南省',
        '420' => '湖北省',
        '430' => '湖南省',
        '440' => '广东省',
        '450' => '广西',
        '460' => '海南省',
        '500' => '重庆市',
        '510' => '四川省',
        '520' => '贵州省',
        '530' => '云南省',
        '540' => '西藏',
        '610' => '陕西省',
        '620' => '甘肃省',
        '630' => '青海省',
        '640' => '宁夏',
        '650' => '新疆',
        '710' => '台湾省',
        '810' => '香港',
        '820' => '澳门'
    ];
    
    $prefix = substr($code, 0, 3);
    return $regions[$prefix] ?? '未知地区';
}
?>
