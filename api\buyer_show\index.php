<?php
/**
 * 买家秀欣赏API
 * 免费功能，获取随机买家秀图片和JSON数据
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $type = $_GET['type'] ?? 'image';
    } else {
        $type = $input['type'] ?? 'image';
    }
    
    // 验证类型参数
    if (!in_array($type, ['image', 'json'])) {
        echo json_encode([
            'code' => 400,
            'message' => '类型参数错误，只支持image或json'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 调用上游API
    $result = fetchBuyerShow($type);
    
    if ($result['success']) {
        if ($type === 'json') {
            // 返回JSON数据
            echo json_encode([
                'code' => 200,
                'message' => '获取买家秀数据成功',
                'type' => 'json',
                'data' => $result['data']
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        } else {
            // 返回图片信息
            echo json_encode([
                'code' => 200,
                'message' => '获取买家秀图片成功',
                'type' => 'image',
                'image_url' => $result['image_url'],
                'image_base64' => $result['image_base64']
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
    } else {
        echo json_encode([
            'code' => 500,
            'message' => '获取买家秀失败：' . $result['error']
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取买家秀数据
 */
function fetchBuyerShow($type) {
    try {
        $apiUrl = 'https://api.03c3.cn/api/taobaoBuyerShow';
        if ($type === 'json') {
            $apiUrl .= '?type=json';
        }
        
        // 设置请求上下文
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 15,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'header' => [
                    'Accept: */*',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8'
                ]
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            return ['success' => false, 'error' => '网络请求失败'];
        }
        
        if ($type === 'json') {
            // 解析JSON数据
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['success' => false, 'error' => 'JSON解析失败'];
            }
            
            return [
                'success' => true,
                'data' => $data
            ];
        } else {
            // 处理图片数据
            $imageBase64 = 'data:image/jpeg;base64,' . base64_encode($response);
            
            // 可选：保存图片到临时目录
            $filename = 'buyer_show_' . time() . '_' . rand(1000, 9999) . '.jpg';
            $filepath = __DIR__ . '/../../tmp/' . $filename;
            
            // 确保tmp目录存在
            $tmpDir = dirname($filepath);
            if (!is_dir($tmpDir)) {
                mkdir($tmpDir, 0755, true);
            }
            
            // 保存图片
            $imageUrl = null;
            if (file_put_contents($filepath, $response)) {
                $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                          '://' . $_SERVER['HTTP_HOST'];
                $imageUrl = $baseUrl . '/tmp/' . $filename;
            }
            
            return [
                'success' => true,
                'image_url' => $imageUrl,
                'image_base64' => $imageBase64
            ];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
