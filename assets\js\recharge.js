// 充值管理系统
class RechargeManager {
    constructor() {
        this.isProcessing = false;
        this.init();
    }

    // 初始化
    init() {
        this.bindEvents();
    }

    // 绑定事件
    bindEvents() {
        // 卡密充值表单提交
        const kamiForm = document.getElementById('kamiForm');
        if (kamiForm) {
            kamiForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleKamiRecharge();
            });
        }
    }

    // 处理卡密充值
    async handleKamiRecharge() {
        if (this.isProcessing) return;

        const kamiInput = document.getElementById('kamiInput');
        const kami = kamiInput.value.trim();

        // 验证输入
        if (!kami) {
            this.showToast('请输入卡密', 'error');
            return;
        }

        // 由于有强制登录机制，直接获取token
        const token = window.AuthManager.getUserToken();

        this.isProcessing = true;
        this.showLoading('正在处理充值...');

        try {
            // 使用本地API代理
            const response = await fetch(`${CONFIG.API.BASE_URL}/api/kami/use.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    kami: kami
                })
            });

            const result = await response.json();

            if (result.code === 200) {
                // 充值成功
                this.showRechargeResult(true, result.message, result.user);
                
                // 更新本地用户信息
                if (window.AuthManager && result.user) {
                    window.AuthManager.updateUserInfo({
                        vipcode: 1,
                        viptime: result.user.viptime
                    });
                }

                // 清空输入框
                kamiInput.value = '';
                
                // 3秒后关闭模态框
                setTimeout(() => {
                    this.closeModal('rechargeModal');
                }, 3000);
                
            } else {
                // 充值失败
                this.showRechargeResult(false, result.message || '充值失败');
            }

        } catch (error) {
            console.error('Recharge error:', error);
            this.showRechargeResult(false, '网络错误，请稍后重试');
        } finally {
            this.isProcessing = false;
            this.hideLoading();
        }
    }

    // 显示充值结果
    showRechargeResult(success, message, userInfo = null) {
        const resultContainer = document.getElementById('rechargeResult');
        if (!resultContainer) return;

        const resultClass = success ? 'success' : 'error';
        const icon = success ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
        
        let content = `
            <div class="recharge-result ${resultClass}">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                    <i class="${icon}" style="font-size: 24px;"></i>
                    <h4 style="margin: 0;">${success ? '充值成功！' : '充值失败'}</h4>
                </div>
                <p style="margin: 0 0 10px 0;">${message}</p>
        `;

        if (success && userInfo) {
            content += `
                <div style="margin-top: 15px; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 8px;">
                    <h5 style="margin: 0 0 10px 0;">会员信息已更新</h5>
                    <p style="margin: 0;"><strong>VIP到期时间：</strong>${userInfo.viptime}</p>
                </div>
            `;
        }

        content += '</div>';
        
        resultContainer.innerHTML = content;
        resultContainer.style.display = 'block';

        // 隐藏充值表单
        const rechargeInfo = document.querySelector('.recharge-info');
        if (rechargeInfo) {
            rechargeInfo.style.display = 'none';
        }
    }

    // 重置充值界面
    resetRechargeInterface() {
        const resultContainer = document.getElementById('rechargeResult');
        const rechargeInfo = document.querySelector('.recharge-info');
        const kamiInput = document.getElementById('kamiInput');

        if (resultContainer) {
            resultContainer.style.display = 'none';
            resultContainer.innerHTML = '';
        }

        if (rechargeInfo) {
            rechargeInfo.style.display = 'block';
        }

        if (kamiInput) {
            kamiInput.value = '';
        }
    }

    // UI辅助方法
    showLoading(message = '加载中...') {
        if (window.UI) {
            window.UI.showLoading(message);
        }
    }

    hideLoading() {
        if (window.UI) {
            window.UI.hideLoading();
        }
    }

    showToast(message, type = 'info') {
        if (window.UI) {
            window.UI.showToast(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    closeModal(modalId) {
        if (window.UI) {
            window.UI.closeModal(modalId);
        } else {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';
            }
        }
    }
}

// 全局函数
function openRecharge() {
    // 由于有强制登录机制，这里不需要检查登录状态

    // 重置充值界面
    if (window.RechargeManager) {
        window.RechargeManager.resetRechargeInterface();
    }

    // 打开充值模态框
    if (window.UI) {
        window.UI.openModal('rechargeModal');
        // 只在移动端设置底栏活动状态
        if (window.innerWidth <= 768) {
            window.UI.setBottomBarActive('openRecharge');
        }
    }
}

function buyKami() {
    // 跳转到购买卡密的Telegram机器人
    const telegramUrl = 'https://t.me/MikaJishouBot?start=8034567958';
    window.open(telegramUrl, '_blank');
    
    if (window.UI) {
        window.UI.showToast('已打开购买页面', 'info');
    }
}

function openOnlineShop() {
    // 跳转到在线商城
    const shopUrl = 'https://cloudshop.qnm6.top/shop.php?sj=8034567958';
    window.open(shopUrl, '_blank');
    
    if (window.UI) {
        window.UI.showToast('已打开在线商城', 'info');
    }
}

// 初始化充值管理器
document.addEventListener('DOMContentLoaded', function() {
    window.RechargeManager = new RechargeManager();
});

