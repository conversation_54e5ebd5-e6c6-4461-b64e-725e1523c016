# QQ绑定Phone查询功能演示

## 功能展示

### 1. 工具界面

新增的QQ绑定Phone查询工具位于VIP工具分类中，具有以下特点：

- **图标**: 使用QQ官方图标 (`fab fa-qq`)
- **名称**: QQ绑定Phone查询
- **描述**: 根据QQ号查询绑定的手机号信息，支持查询QQ关联的手机号码
- **权限**: VIP专享功能

### 2. 输入界面

用户界面包含：
- QQ号输入框（必填）
- 占位符提示：请输入5-11位QQ号码
- 实时格式验证

### 3. 查询流程

1. **输入验证**
   - 检查QQ号是否为空
   - 验证QQ号格式（5-11位数字）
   - 显示相应的错误提示

2. **权限验证**
   - 检查用户是否已登录
   - 验证用户是否具有VIP权限
   - 自动添加用户Token到请求

3. **API调用**
   - 发送POST请求到后端API
   - 显示加载状态
   - 处理响应结果

### 4. 结果展示

#### 成功查询
```
QQ绑定Phone查询结果

QQ: 10001
查询到以下结果↓:

qq: 10001
手机号: 13688805189

查询耗时：3.4318 秒
```

#### 错误处理
- 参数错误：显示具体的错误信息
- 权限不足：提示升级VIP
- 网络错误：显示网络连接问题
- 服务不可用：提示稍后重试

### 5. 样式特色

- **蓝色主题**: 使用QQ品牌色彩 (#1890ff)
- **图标装饰**: QQ图标和时钟图标
- **代码风格**: 等宽字体显示查询结果
- **响应式设计**: 适配不同屏幕尺寸

## 技术亮点

### 1. 前端验证
```javascript
// QQ号格式验证
validateQQ: function(qq) {
    const pattern = /^\d{5,11}$/;
    return pattern.test(qq);
}
```

### 2. 后端处理
```php
// QQ号格式验证
if (!preg_match('/^\d{5,11}$/', $qq)) {
    echo json_encode([
        'code' => 400,
        'message' => 'QQ号格式不正确，请输入5-11位数字'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
```

### 3. 结果处理
```javascript
// 特殊的QQ查询结果处理
else if (tool.id === 'qq_phone_query' && result.shuju) {
    content += `<div class="qq-phone-results">
        <h5><i class="fab fa-qq"></i> QQ绑定Phone查询结果</h5>
        <div class="qq-phone-content">
            <pre class="qq-phone-text">${result.shuju}</pre>
        </div>`;
    // ...
}
```

## 使用示例

### 示例1：正常查询
- **输入**: QQ号 `10001`
- **输出**: 显示QQ号对应的手机号信息
- **状态**: 查询成功

### 示例2：格式错误
- **输入**: QQ号 `123`
- **输出**: "QQ号格式不正确，请输入5-11位数字"
- **状态**: 验证失败

### 示例3：权限不足
- **输入**: 普通用户尝试查询
- **输出**: "权限不足，请升级VIP"
- **状态**: 权限验证失败

## 安全考虑

1. **输入验证**: 严格验证QQ号格式
2. **权限控制**: VIP权限验证
3. **错误处理**: 不暴露敏感信息
4. **请求限制**: 依赖上游API的频率限制
5. **数据传输**: 使用HTTPS确保安全

## 扩展可能

1. **批量查询**: 支持多个QQ号同时查询
2. **历史记录**: 保存查询历史
3. **导出功能**: 支持结果导出
4. **缓存机制**: 减少重复查询
5. **统计分析**: 查询成功率统计

## 兼容性

- **浏览器**: 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- **移动端**: 响应式设计，支持手机和平板
- **API**: 兼容现有的API架构
- **样式**: 与现有UI风格保持一致

## 部署说明

1. 确保PHP环境支持cURL扩展
2. 配置正确的上游API地址
3. 检查文件权限设置
4. 测试API连通性
5. 验证VIP权限系统
