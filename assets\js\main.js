// 主应用程序
class Application {
    constructor() {
        this.isInitialized = false;
        this.profileManager = null;
        this.init();
    }

    // 初始化应用
    async init() {
        if (this.isInitialized) return;

        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.init());
                return;
            }

            // 初始化个人中心管理器
            this.profileManager = new ProfileManager();
            window.ProfileManager = this.profileManager;

            // 设置全局错误处理
            this.setupErrorHandling();

            // 设置性能监控
            this.setupPerformanceMonitoring();

            // 标记为已初始化
            this.isInitialized = true;

            console.log('iDatas应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
            UI.showToast('应用初始化失败，请刷新页面重试', 'error');
        }
    }

    // 设置错误处理
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            if (window.UI) {
                window.UI.showToast('发生了一个错误，请刷新页面重试', 'error');
            }
        });

        // Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise错误:', event.reason);
            if (window.UI) {
                window.UI.showToast('网络请求失败，请检查网络连接', 'error');
            }
        });
    }

    // 设置性能监控
    setupPerformanceMonitoring() {
        // 页面加载性能
        window.addEventListener('load', () => {
            if (performance.timing) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`页面加载时间: ${loadTime}ms`);
            }
        });
    }
}

// 个人中心管理器
class ProfileManager {
    constructor() {
        this.userProfile = null;
        this.init();
    }

    // 初始化
    init() {
        // 初始化时不需要做什么，等待用户点击个人中心时再加载
    }

    // 显示个人中心
    async showProfile() {
        if (!window.AuthManager || !window.AuthManager.isLoggedIn) {
            this.showToast('请先登录', 'warning');
            return;
        }

        this.showLoading('加载用户信息...');

        try {
            await this.loadUserProfile();
            this.renderProfile();
            this.openModal('profileModal');
        } catch (error) {
            this.showToast('加载用户信息失败', 'error');
            console.error('Load profile error:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 加载用户资料
    async loadUserProfile() {
        const token = window.AuthManager.getUserToken();
        if (!token) {
            throw new Error('用户未登录');
        }

        try {
            // 从上游API获取用户信息
            const response = await fetch(`${CONFIG.API.UPSTREAM_BASE_URL}${CONFIG.API.ENDPOINTS.UPSTREAM_USER_INFO}?token=${token}`);
            const result = await response.json();

            if (result.code === 200) {
                this.userProfile = {
                    ...window.AuthManager.getCurrentUser(),
                    ...result.user,
                    msg: result.msg,
                    today_launch_count: result.today_launch_count
                };
            } else {
                // 如果上游API失败，使用本地用户信息
                this.userProfile = window.AuthManager.getCurrentUser();
            }
        } catch (error) {
            console.error('获取上游用户信息失败:', error);
            // 使用本地用户信息作为降级方案
            this.userProfile = window.AuthManager.getCurrentUser();
        }
    }

    // 渲染个人中心
    renderProfile() {
        const modalBody = document.getElementById('profileModalBody');
        if (!modalBody || !this.userProfile) return;

        const isVIP = window.AuthManager.isVIP();
        const vipStatus = isVIP ? 'VIP用户' : '普通用户';
        const vipBadgeClass = isVIP ? 'vip-badge' : 'normal-badge';

        modalBody.innerHTML = `
            <div class="profile-info">
                <div class="profile-card">
                    <h4>基本信息</h4>
                    <div class="profile-item">
                        <span class="profile-label">用户名</span>
                        <span class="profile-value">${this.userProfile.username || 'N/A'}</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-label">用户Token</span>
                        <span class="profile-value">
                            ${this.userProfile.token || 'N/A'}
                            <button class="btn-copy" id="copyTokenBtn" data-token="${this.userProfile.token}" title="复制Token">
                                <i class="fas fa-copy"></i>
                            </button>
                        </span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-label">注册时间</span>
                        <span class="profile-value">${this.formatDate(this.userProfile.created_at || this.userProfile.time)}</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-label">最后登录</span>
                        <span class="profile-value">${this.formatDate(this.userProfile.last_login)}</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-label">登录次数</span>
                        <span class="profile-value">${this.userProfile.login_count || 0}</span>
                    </div>
                </div>

                <div class="profile-card">
                    <h4>会员信息</h4>
                    <div class="profile-item">
                        <span class="profile-label">会员状态</span>
                        <span class="profile-value">
                            <span class="${vipBadgeClass}">${vipStatus}</span>
                        </span>
                    </div>
                    ${isVIP ? `
                    <div class="profile-item">
                        <span class="profile-label">VIP到期时间</span>
                        <span class="profile-value">${this.formatDate(this.userProfile.viptime)}</span>
                    </div>
                    ` : ''}
                    <div class="profile-item">
                        <span class="profile-label">账户状态</span>
                        <span class="profile-value">${this.getAccountStatus(this.userProfile.tokencode)}</span>
                    </div>
                </div>

                ${this.userProfile.today_launch_count ? `
                <div class="profile-card">
                    <h4>使用统计</h4>
                    <div class="profile-item">
                        <span class="profile-label">今日启动次数</span>
                        <span class="profile-value">${this.userProfile.today_launch_count}</span>
                    </div>
                </div>
                ` : ''}

                ${this.userProfile.msg ? `
                <div class="profile-card">
                    <h4>系统公告</h4>
                    <div class="profile-item">
                        <span class="profile-label">应用版本</span>
                        <span class="profile-value">${this.userProfile.msg.appvid}</span>
                    </div>
                    <div class="profile-item">
                        <span class="profile-label">应用状态</span>
                        <span class="profile-value">${this.userProfile.msg.appcode === '1' ? '正常' : '维护中'}</span>
                    </div>
                    ${this.userProfile.msg.appgg ? `
                    <div class="profile-announcement">
                        <p>${this.userProfile.msg.appgg.replace(/\r\n/g, '<br>')}</p>
                    </div>
                    ` : ''}
                </div>
                ` : ''}

                <div class="profile-actions">
                    <button class="btn btn-secondary" id="refreshProfileBtn">
                        <i class="fas fa-sync-alt"></i>
                        刷新信息
                    </button>
                    <button class="btn btn-danger" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        退出登录
                    </button>
                </div>
            </div>
        `;

        // 添加复制按钮样式
        const style = document.createElement('style');
        style.textContent = `
            .btn-copy {
                background: none;
                border: none;
                color: var(--primary-color);
                cursor: pointer;
                margin-left: 8px;
                padding: 4px;
                border-radius: 4px;
                transition: var(--transition-fast);
            }
            .btn-copy:hover {
                background: var(--bg-secondary);
            }
            .profile-announcement {
                margin-top: 12px;
                padding: 12px;
                background: var(--bg-secondary);
                border-radius: var(--radius-small);
                font-size: 14px;
                line-height: 1.5;
            }
            .profile-actions {
                display: flex;
                gap: 12px;
                margin-top: 20px;
                justify-content: center;
            }
        `;
        document.head.appendChild(style);

        // 绑定事件
        setTimeout(() => {
            // 绑定刷新按钮事件
            const refreshBtn = document.getElementById('refreshProfileBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    this.refreshProfile();
                });
            }

            // 绑定复制按钮事件
            const copyBtn = document.getElementById('copyTokenBtn');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => {
                    const token = copyBtn.getAttribute('data-token');
                    if (token) {
                        copyToClipboard(token);
                    }
                });
            }
        }, 100);
    }

    // 刷新个人资料
    async refreshProfile() {
        this.showLoading('刷新中...');
        try {
            await this.loadUserProfile();
            this.renderProfile();
            this.showToast('信息已刷新', 'success');
        } catch (error) {
            this.showToast('刷新失败', 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return dateString;
        }
    }

    // 获取账户状态
    getAccountStatus(tokencode) {
        switch (parseInt(tokencode)) {
            case 200:
                return '正常';
            case 500:
                return '受限';
            default:
                return '未知';
        }
    }

    // UI辅助方法
    showLoading(message = '加载中...') {
        if (window.UI) {
            window.UI.showLoading(message);
        }
    }

    hideLoading() {
        if (window.UI) {
            window.UI.hideLoading();
        }
    }

    showToast(message, type = 'info') {
        if (window.UI) {
            window.UI.showToast(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    openModal(modalId) {
        if (window.UI) {
            window.UI.openModal(modalId);
        } else {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
        }
    }
}

// 工具函数
function refreshProfile() {
    if (window.ProfileManager) {
        window.ProfileManager.refreshProfile();
    }
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        if (window.UI) {
            window.UI.showToast('已复制到剪贴板', 'success');
        } else {
            alert('已复制到剪贴板');
        }
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        if (window.UI) {
            window.UI.showToast('已复制到剪贴板', 'success');
        } else {
            alert('已复制到剪贴板');
        }
    }
}

// 悬浮竖幅管理器
class FloatingBannerManager {
    constructor() {
        this.banner = null;
        this.mini = null;
        this.isMinimized = false;
        this.isMobile = window.innerWidth <= 768;
        this.init();
        this.setupResponsive();
    }

    init() {
        this.banner = document.getElementById('floatingVipBanner');
        this.mini = document.getElementById('floatingVipMini');

        // 检查是否之前被最小化过
        const bannerState = localStorage.getItem('vipBannerState');
        if (bannerState === 'minimized') {
            this.minimizeBanner(null, false);
        } else {
            // 移动端延迟更长时间，桌面端较短
            const delay = this.isMobile ? 3000 : 2000;
            setTimeout(() => {
                this.showBanner();
            }, delay);
        }
    }

    // 设置响应式监听
    setupResponsive() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth <= 768;

            // 如果从桌面切换到移动端，或反之，重新调整状态
            if (wasMobile !== this.isMobile && !this.isClosed) {
                this.adjustForScreenSize();
            }
        });
    }

    // 根据屏幕尺寸调整
    adjustForScreenSize() {
        if (this.isMobile) {
            // 移动端：如果是最小化状态，调整小图标位置
            if (this.isMinimized && this.mini) {
                this.mini.style.transition = 'all 0.3s ease';
            }
            // 添加移动端滑动手势
            this.setupMobileGestures();
        }
    }

    // 设置移动端手势
    setupMobileGestures() {
        if (!this.banner || !this.isMobile) return;

        let startY = 0;
        let startTime = 0;
        let isDragging = false;

        // 触摸开始
        this.banner.addEventListener('touchstart', (e) => {
            if (e.target.closest('.banner-minimize')) {
                return; // 不处理按钮区域的触摸
            }

            startY = e.touches[0].clientY;
            startTime = Date.now();
            isDragging = false;
        }, { passive: true });

        // 触摸移动
        this.banner.addEventListener('touchmove', (e) => {
            if (e.target.closest('.banner-minimize')) {
                return;
            }

            const currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;

            if (Math.abs(deltaY) > 10) {
                isDragging = true;
            }
        }, { passive: true });

        // 触摸结束
        this.banner.addEventListener('touchend', (e) => {
            if (e.target.closest('.banner-minimize')) {
                return;
            }

            const endTime = Date.now();
            const duration = endTime - startTime;

            // 如果是快速向下滑动，最小化横幅
            if (isDragging && duration < 300) {
                const currentY = e.changedTouches[0].clientY;
                const deltaY = currentY - startY;

                if (deltaY > 50) { // 向下滑动超过50px
                    this.minimizeBanner(null, true);
                    return;
                }
            }

            // 如果不是拖拽，则触发点击事件
            if (!isDragging && duration < 300) {
                openRecharge();
            }

            isDragging = false;
        }, { passive: true });
    }

    showBanner() {
        if (this.banner) {
            this.banner.classList.remove('hidden', 'minimized');
            this.isMinimized = false;

            // 移动端自动隐藏功能
            if (this.isMobile) {
                this.setupAutoHide();
            }
        }
    }

    // 移动端自动隐藏设置
    setupAutoHide() {
        // 清除之前的定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
        }

        // 10秒后自动最小化（移动端）
        this.autoHideTimer = setTimeout(() => {
            if (!this.isMinimized && this.isMobile) {
                this.minimizeBanner(null, false); // 不保存状态，只是临时隐藏
            }
        }, 10000);
    }

    // 重置自动隐藏定时器
    resetAutoHide() {
        if (this.isMobile && this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.setupAutoHide();
        }
    }



    minimizeBanner(event, saveState = true) {
        if (event) {
            event.stopPropagation();
        }

        if (this.banner) {
            this.banner.classList.add('minimized');
            this.isMinimized = true;
        }

        // 显示小图标
        setTimeout(() => {
            if (this.mini) {
                this.mini.classList.add('show');
            }
        }, 300);

        if (saveState) {
            localStorage.setItem('vipBannerState', 'minimized');
        }
    }

    restoreBanner() {
        if (this.banner && this.isMinimized) {
            this.banner.classList.remove('minimized');
            this.isMinimized = false;
        }

        if (this.mini) {
            this.mini.classList.remove('show');
        }

        localStorage.setItem('vipBannerState', 'normal');
    }

    // 重置状态（用于测试或特殊情况）
    resetBanner() {
        localStorage.removeItem('vipBannerState');
        this.isMinimized = false;
        this.showBanner();
        if (this.mini) {
            this.mini.classList.remove('show');
        }
    }
}

// 全局函数供HTML调用
function minimizeBanner(event) {
    if (window.FloatingBannerManager) {
        window.FloatingBannerManager.minimizeBanner(event);
    }
}

function restoreBanner() {
    if (window.FloatingBannerManager) {
        window.FloatingBannerManager.restoreBanner();
    }
}

// 会员激励弹窗管理器
class MemberIncentiveManager {
    constructor() {
        this.popup = null;
        this.usageCount = 0;
        this.hasShownFirst = false;
        this.storageKey = 'memberIncentiveData';
        this.init();
    }

    init() {
        this.popup = document.getElementById('memberIncentivePopup');
        this.loadStorageData();
        this.setupEventListeners();
    }

    // 加载本地存储数据
    loadStorageData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            if (data) {
                const parsed = JSON.parse(data);
                this.usageCount = parsed.usageCount || 0;
                this.hasShownFirst = parsed.hasShownFirst || false;
            }
        } catch (error) {
            console.warn('Failed to load member incentive data:', error);
        }
    }

    // 保存数据到本地存储
    saveStorageData() {
        try {
            const data = {
                usageCount: this.usageCount,
                hasShownFirst: this.hasShownFirst
            };
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save member incentive data:', error);
        }
    }

    // 设置事件监听
    setupEventListeners() {
        // 监听工具使用事件
        document.addEventListener('toolUsageTracked', () => {
            this.handleToolUsage();
        });

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isPopupVisible()) {
                this.hidePopup();
            }
        });
    }

    // 处理工具使用
    handleToolUsage() {
        this.usageCount++;
        this.saveStorageData();

        // 首次使用或每四次使用后显示弹窗
        if (!this.hasShownFirst || this.usageCount % 4 === 0) {
            // 延迟显示，避免与其他提示冲突
            setTimeout(() => {
                this.showPopup();
            }, 1500);
        }
    }

    // 显示弹窗
    showPopup() {
        if (!this.popup || this.isPopupVisible()) {
            return;
        }

        // 检查是否已经是VIP用户
        if (this.isVipUser()) {
            return;
        }

        this.popup.classList.add('active');
        document.body.style.overflow = 'hidden'; // 防止背景滚动

        // 标记首次已显示
        if (!this.hasShownFirst) {
            this.hasShownFirst = true;
            this.saveStorageData();
        }

        // 触发显示事件
        this.dispatchEvent('memberPopupShown');
    }

    // 隐藏弹窗
    hidePopup() {
        if (!this.popup || !this.isPopupVisible()) {
            return;
        }

        this.popup.classList.remove('active');
        document.body.style.overflow = ''; // 恢复背景滚动

        // 触发隐藏事件
        this.dispatchEvent('memberPopupHidden');
    }

    // 检查弹窗是否可见
    isPopupVisible() {
        return this.popup && this.popup.classList.contains('active');
    }

    // 检查是否为VIP用户
    isVipUser() {
        try {
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
            return userInfo.isVip === true;
        } catch (error) {
            return false;
        }
    }

    // 触发自定义事件
    dispatchEvent(eventName, detail = {}) {
        document.dispatchEvent(new CustomEvent(eventName, { detail }));
    }

    // 重置数据（用于测试）
    resetData() {
        this.usageCount = 0;
        this.hasShownFirst = false;
        localStorage.removeItem(this.storageKey);
    }

    // 强制显示弹窗（用于测试）
    forceShow() {
        this.showPopup();
    }

    // 获取当前状态
    getStatus() {
        return {
            usageCount: this.usageCount,
            hasShownFirst: this.hasShownFirst,
            isVisible: this.isPopupVisible(),
            isVip: this.isVipUser()
        };
    }
}

// 公告栏管理器
class AnnouncementManager {
    constructor() {
        this.toggle = null;
        this.content = null;
        this.isCollapsed = false;
        this.storageKey = 'announcementCollapsed';
        this.init();
    }

    init() {
        this.toggle = document.getElementById('announcementToggle');
        this.content = document.getElementById('announcementContent');

        if (!this.toggle || !this.content) {
            return;
        }

        // 加载保存的状态
        this.loadState();

        // 绑定事件
        this.setupEventListeners();
    }

    // 加载保存的状态
    loadState() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved === 'true') {
                this.collapse();
            }
        } catch (error) {
            console.warn('Failed to load announcement state:', error);
        }
    }

    // 保存状态
    saveState() {
        try {
            localStorage.setItem(this.storageKey, this.isCollapsed.toString());
        } catch (error) {
            console.warn('Failed to save announcement state:', error);
        }
    }

    // 设置事件监听
    setupEventListeners() {
        this.toggle.addEventListener('click', () => {
            this.toggleCollapse();
        });
    }

    // 切换折叠状态
    toggleCollapse() {
        if (this.isCollapsed) {
            this.expand();
        } else {
            this.collapse();
        }
    }

    // 折叠公告栏
    collapse() {
        this.isCollapsed = true;
        this.content.classList.add('collapsed');
        this.toggle.classList.add('collapsed');
        this.saveState();
    }

    // 展开公告栏
    expand() {
        this.isCollapsed = false;
        this.content.classList.remove('collapsed');
        this.toggle.classList.remove('collapsed');
        this.saveState();
    }

    // 获取当前状态
    getState() {
        return {
            isCollapsed: this.isCollapsed
        };
    }
}

// 全局函数供HTML调用
function closeMemberPopup() {
    if (window.MemberIncentiveManager) {
        window.MemberIncentiveManager.hidePopup();
    }
}

function upgradeMembership() {
    // 关闭弹窗
    closeMemberPopup();

    // 打开充值页面（与底栏充值相同）
    if (window.openRecharge) {
        window.openRecharge();
    }
}

function showVipBenefits() {
    // 显示会员特权弹窗
    if (window.MemberIncentiveManager) {
        window.MemberIncentiveManager.forceShow();
    }
}

// 启动应用
window.addEventListener('DOMContentLoaded', function() {
    window.App = new Application();
    window.FloatingBannerManager = new FloatingBannerManager();
    window.MemberIncentiveManager = new MemberIncentiveManager();
    window.AnnouncementManager = new AnnouncementManager();
});

// 导出到全局
window.Application = Application;
window.ProfileManager = ProfileManager;
window.FloatingBannerManager = FloatingBannerManager;
window.MemberIncentiveManager = MemberIncentiveManager;
window.AnnouncementManager = AnnouncementManager;
