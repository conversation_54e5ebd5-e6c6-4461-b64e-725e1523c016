/**
 * 导航栏搜索功能
 */

class NavSearchManager {
    constructor() {
        this.searchInput = null;
        this.searchClear = null;
        this.searchDropdown = null;
        this.searchResults = null;
        this.isSearchActive = false;
        this.searchTimeout = null;
        this.init();
    }

    // 初始化搜索功能
    init() {
        this.bindElements();
        this.bindEvents();
    }

    // 绑定DOM元素
    bindElements() {
        this.searchInput = document.getElementById('navSearchInput');
        this.searchClear = document.getElementById('searchClear');
        this.searchDropdown = document.getElementById('searchDropdown');
        this.searchResults = document.getElementById('searchResults');
    }

    // 绑定事件
    bindEvents() {
        if (!this.searchInput) return;

        // 输入事件
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // 焦点事件
        this.searchInput.addEventListener('focus', () => {
            if (this.searchInput.value.trim()) {
                this.showDropdown();
            }
        });

        // 失焦事件（延迟隐藏，允许点击结果）
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideDropdown();
            }, 200);
        });

        // 清除按钮事件
        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // 键盘事件
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.nav-search')) {
                this.hideDropdown();
            }
        });
    }

    // 处理搜索输入
    handleSearchInput(query) {
        const trimmedQuery = query.trim();

        // 显示/隐藏清除按钮
        if (this.searchClear) {
            this.searchClear.style.display = trimmedQuery ? 'block' : 'none';
        }

        // 清除之前的搜索定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 延迟搜索，避免频繁查询
        this.searchTimeout = setTimeout(() => {
            if (trimmedQuery) {
                this.performSearch(trimmedQuery);
                this.showDropdown();
            } else {
                this.hideDropdown();
            }
        }, 300);
    }

    // 执行搜索
    performSearch(query) {
        if (!window.ToolsManager || !window.ToolsManager.tools) {
            this.displayNoResults();
            return;
        }

        const tools = window.ToolsManager.tools;
        const results = tools.filter(tool => {
            const nameMatch = tool.name.toLowerCase().includes(query.toLowerCase());
            const descMatch = tool.description.toLowerCase().includes(query.toLowerCase());
            return nameMatch || descMatch;
        });

        this.displayResults(results, query);
    }

    // 显示搜索结果
    displayResults(results, query) {
        if (!this.searchResults) return;

        if (results.length === 0) {
            this.displayNoResults();
            return;
        }

        const resultsHtml = results.map(tool => {
            const badgeClass = tool.vip ? 'vip' : 'free';
            const badgeText = tool.vip ? 'VIP专享' : '免费';

            return `
                <div class="search-result-item" data-tool-id="${tool.id}">
                    <div class="search-result-icon">
                        ${this.renderSearchIcon(tool.icon)}
                    </div>
                    <div class="search-result-content">
                        <div class="search-result-title">${this.highlightText(tool.name, query)}</div>
                        <div class="search-result-description">${this.highlightText(tool.description, query)}</div>
                    </div>
                    <div class="search-result-badge ${badgeClass}">${badgeText}</div>
                </div>
            `;
        }).join('');

        this.searchResults.innerHTML = resultsHtml;

        // 绑定结果点击事件
        this.bindResultEvents();
    }

    // 显示无结果
    displayNoResults() {
        if (!this.searchResults) return;

        this.searchResults.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
                <div>未找到匹配的功能工具</div>
                <div style="font-size: 12px; margin-top: 4px;">请尝试其他关键词</div>
            </div>
        `;
    }

    // 渲染搜索结果图标
    renderSearchIcon(icon) {
        if (!icon) {
            return '<i class="fas fa-cog"></i>'; // 默认图标
        }

        // 检查是否为外部图片资源
        if (icon.startsWith('http://') ||
            icon.startsWith('https://') ||
            icon.startsWith('data:image/') ||
            icon.startsWith('/') ||
            icon.endsWith('.png') ||
            icon.endsWith('.jpg') ||
            icon.endsWith('.jpeg') ||
            icon.endsWith('.svg') ||
            icon.endsWith('.gif') ||
            icon.endsWith('.webp')) {
            return `<img src="${icon}" alt="工具图标" class="search-icon-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <i class="fas fa-cog" style="display: none;"></i>`;
        } else {
            // Font Awesome 图标
            return `<i class="${icon}"></i>`;
        }
    }

    // 高亮搜索关键词
    highlightText(text, query) {
        if (!query) return text;

        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark style="background: var(--primary-light); color: var(--primary-color); padding: 0 2px; border-radius: 2px;">$1</mark>');
    }

    // 绑定搜索结果点击事件
    bindResultEvents() {
        const resultItems = this.searchResults.querySelectorAll('.search-result-item');
        resultItems.forEach(item => {
            item.addEventListener('click', () => {
                const toolId = item.dataset.toolId;
                this.selectTool(toolId);
            });
        });
    }

    // 选择工具
    selectTool(toolId) {
        // 清除搜索
        this.clearSearch();
        
        // 打开工具
        if (window.ToolsManager) {
            window.ToolsManager.openTool(toolId);
        }
    }

    // 处理键盘事件
    handleKeydown(e) {
        if (!this.isSearchActive) return;

        const items = this.searchResults.querySelectorAll('.search-result-item');
        const currentActive = this.searchResults.querySelector('.search-result-item.active');
        let activeIndex = -1;

        if (currentActive) {
            activeIndex = Array.from(items).indexOf(currentActive);
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                activeIndex = Math.min(activeIndex + 1, items.length - 1);
                this.setActiveItem(items, activeIndex);
                break;

            case 'ArrowUp':
                e.preventDefault();
                activeIndex = Math.max(activeIndex - 1, 0);
                this.setActiveItem(items, activeIndex);
                break;

            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    const toolId = currentActive.dataset.toolId;
                    this.selectTool(toolId);
                }
                break;

            case 'Escape':
                this.clearSearch();
                this.searchInput.blur();
                break;
        }
    }

    // 设置活动项
    setActiveItem(items, index) {
        items.forEach(item => item.classList.remove('active'));
        if (items[index]) {
            items[index].classList.add('active');
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }

    // 显示下拉框
    showDropdown() {
        if (this.searchDropdown) {
            this.searchDropdown.style.display = 'block';
            this.isSearchActive = true;
        }
    }

    // 隐藏下拉框
    hideDropdown() {
        if (this.searchDropdown) {
            this.searchDropdown.style.display = 'none';
            this.isSearchActive = false;
        }
    }

    // 清除搜索
    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        if (this.searchClear) {
            this.searchClear.style.display = 'none';
        }
        this.hideDropdown();
    }

    // 获取搜索建议（可扩展功能）
    getSearchSuggestions(query) {
        // 可以添加搜索建议逻辑
        return [];
    }
}

// 全局初始化
document.addEventListener('DOMContentLoaded', function() {
    window.NavSearchManager = new NavSearchManager();
});
