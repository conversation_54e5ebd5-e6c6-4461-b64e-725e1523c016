/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 - 蓝色系专业配色 */
:root {
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --primary-light: #4DA3FF;
    --secondary-color: #5AC8FA;
    --accent-color: #34C759;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    
    --bg-primary: #FFFFFF;
    --bg-secondary: #F2F2F7;
    --bg-tertiary: #FFFFFF;
    --bg-card: #FFFFFF;
    
    --text-primary: #000000;
    --text-secondary: #3C3C43;
    --text-tertiary: #8E8E93;
    --text-quaternary: #C7C7CC;
    
    --border-color: #E5E5EA;
    --separator-color: #C6C6C8;
    
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.2);
    
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 20px;
    
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 基础字体和布局 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    background: var(--bg-primary);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-light);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-img {
    width: 32px;
    height: 32px;
    color: var(--text-primary);
    transition: var(--transition-fast);
}

.logo-img:hover {
    color: var(--primary-color);
    transform: scale(1.05);
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.nav-search {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
    position: relative;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 10px 40px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-large);
    background: var(--bg-secondary);
    font-size: 14px;
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-input::placeholder {
    color: var(--text-tertiary);
}

.search-icon {
    position: absolute;
    left: 14px;
    color: var(--text-tertiary);
    font-size: 14px;
    pointer-events: none;
}

.search-clear {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.search-clear:hover {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* 搜索下拉框 */
.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    z-index: 1001;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;
}

.search-results {
    padding: 8px 0;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition-fast);
    border-bottom: 1px solid var(--border-light);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover,
.search-result-item.active {
    background: var(--bg-secondary);
}

.search-result-item.active {
    border-left: 3px solid var(--primary-color);
}

.search-result-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--radius-small);
    margin-right: 12px;
    color: var(--primary-color);
}

.search-result-content {
    flex: 1;
}

.search-result-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.search-result-description {
    font-size: 12px;
    color: var(--text-tertiary);
    line-height: 1.4;
}

.search-result-badge {
    padding: 2px 6px;
    border-radius: var(--radius-small);
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
}

.search-result-badge.free {
    background: var(--accent-color);
    color: white;
}

.search-result-badge.vip {
    background: var(--warning-color);
    color: white;
}

.search-no-results {
    padding: 20px;
    text-align: center;
    color: var(--text-tertiary);
    font-size: 14px;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.tutorial-btn, .profile-btn, .logout-btn, .recharge-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-medium);
    background: transparent;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.tutorial-btn:hover, .profile-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.recharge-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #333;
    font-weight: 600;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.recharge-btn:hover {
    background: linear-gradient(135deg, #FFA500, #FF8C00);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.recharge-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.recharge-btn:hover::before {
    left: 100%;
}

/* VIP状态指示器 */
.profile-btn.vip::after {
    content: 'VIP';
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #333;
    font-size: 10px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    border: 2px solid var(--bg-primary);
}

.profile-btn {
    position: relative;
}

.logout-btn:hover {
    background: var(--danger-color);
    color: white;
}

.mobile-menu-btn {
    display: none;
    padding: 8px;
    cursor: pointer;
    color: var(--text-secondary);
}

/* 主要内容区域 */
.main-content {
    padding: 40px 0;
}

.welcome-section {
    text-align: center;
    margin-bottom: 60px;
}

.welcome-title {
    font-size: 36px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    font-weight: 400;
}

/* 工具展示区 */
.tools-section {
    margin-bottom: 60px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.section-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
}

.filter-tabs {
    display: flex;
    gap: 8px;
    background: var(--bg-secondary);
    padding: 4px;
    border-radius: var(--radius-medium);
}

.filter-tab {
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-small);
    background: transparent;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.filter-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.filter-tab:not(.active):hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

/* 工具网格布局 */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}

.tool-card {
    background: var(--bg-card);
    border-radius: var(--radius-large);
    padding: 24px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-light);
}

/* VIP功能卡片样式 */
.tool-card.vip {
    border: 2px solid var(--warning-color);
    background: linear-gradient(135deg, #FFF9E6, #FFFFFF);
}

.tool-card.vip::before {
    content: 'VIP专享';
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--warning-color);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
}

/* 普通功能卡片样式 */
.tool-card:not(.vip) {
    position: relative;
}

.tool-card:not(.vip)::before {
    content: '免费';
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
}

.tool-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    border-radius: var(--radius-medium);
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    position: relative;
    overflow: hidden;
}

/* 工具图标图片样式 */
.tool-icon-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-medium);
}

/* 搜索结果图标图片样式 */
.search-icon-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-small);
}

.tool-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.tool-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    min-height: 44px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #E6342A;
    transform: translateY(-1px);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    background: var(--bg-primary);
    font-size: 16px;
    transition: var(--transition-fast);
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 2000;
    opacity: 0;
    transition: var(--transition-medium);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

/* 强制登录状态样式 */
body.auth-required {
    overflow: hidden;
}

body.auth-required .modal#loginModal,
body.auth-required .modal#registerModal {
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(12px);
    pointer-events: all;
}

/* 强制登录状态下禁用关闭按钮 */
body.auth-required .modal#loginModal .modal-close,
body.auth-required .modal#registerModal .modal-close {
    display: none !important;
}

/* 强制登录状态下的模态框内容增强 */
body.auth-required .modal#loginModal .modal-content,
body.auth-required .modal#registerModal .modal-content {
    border: 2px solid var(--primary-color);
    box-shadow: 0 20px 60px rgba(0, 122, 255, 0.3);
    animation: auth-required-pulse 2s ease-in-out infinite;
}

/* 强制登录提示动画 */
@keyframes auth-required-pulse {
    0%, 100% {
        box-shadow: 0 20px 60px rgba(0, 122, 255, 0.3);
    }
    50% {
        box-shadow: 0 20px 60px rgba(0, 122, 255, 0.5);
    }
}

/* 强制登录提示文字 */
body.auth-required .modal#loginModal .modal-header::after {
    content: "🔒 请登录后继续使用系统";
    display: block;
    font-size: 12px;
    color: var(--primary-color);
    margin-top: 8px;
    font-weight: normal;
    text-align: center;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
}

body.auth-required .modal#registerModal .modal-header::after {
    content: "🔒 请注册账号后继续使用系统";
    display: block;
    font-size: 12px;
    color: var(--primary-color);
    margin-top: 8px;
    font-weight: normal;
    text-align: center;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--transition-medium);
}

.modal.show .modal-content {
    transform: scale(1);
}

.tool-modal-content, .tutorial-modal-content {
    max-width: 800px;
    width: 95%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: var(--bg-secondary);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-tertiary);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: var(--danger-color);
    color: white;
}

.modal-body {
    padding: 0 24px 24px;
}

/* 视频容器 */
.video-container {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.video-container video {
    width: 100%;
    height: auto;
    display: block;
}

.tutorial-description h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.tutorial-description p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 加载动画 */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    z-index: 3000;
    align-items: center;
    justify-content: center;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: 14px;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-heavy);
    padding: 16px 20px;
    z-index: 4000;
    transform: translateX(400px);
    transition: var(--transition-medium);
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 18px;
}

.toast.success .toast-icon {
    color: var(--accent-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--primary-color);
}

.toast-message {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

/* 个人中心样式 */
.profile-info {
    display: grid;
    gap: 20px;
}

.profile-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    padding: 20px;
}

.profile-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.profile-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.profile-item:last-child {
    border-bottom: none;
}

.profile-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.profile-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.vip-badge {
    background: linear-gradient(135deg, var(--warning-color), #FFB84D);
    color: white;
    padding: 4px 12px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 600;
}

.normal-badge {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 500;
}

/* 工具内容样式 */
.tool-content {
    padding: 20px 0;
}

.tool-form {
    display: grid;
    gap: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.result-container {
    margin-top: 24px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

/* 图片加载相关样式 */
.result-image-container {
    position: relative;
    margin: 16px 0;
    border-radius: var(--radius-medium);
    overflow: hidden;
    background: var(--bg-secondary);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.result-image.loaded {
    opacity: 1;
}

.image-loading-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    color: var(--text-tertiary);
    z-index: 1;
}

.image-loading-placeholder.hidden {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease-out;
}

.image-loading-spinner {
    width: 32px;
    height: 32px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.image-loading-text {
    font-size: 14px;
    color: var(--text-tertiary);
    text-align: center;
}

/* 骨架屏效果 */
.image-skeleton {
    width: 100%;
    height: 300px;
    background: linear-gradient(90deg,
        var(--bg-secondary) 25%,
        var(--border-color) 50%,
        var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-medium);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 图片加载失败样式 */
.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-tertiary);
    background: var(--bg-secondary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-medium);
    min-height: 200px;
}

.image-error i {
    font-size: 48px;
    color: var(--text-quaternary);
    margin-bottom: 12px;
}

.image-error-text {
    font-size: 14px;
    text-align: center;
    line-height: 1.4;
}

.image-retry-btn {
    margin-top: 12px;
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-small);
    font-size: 12px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.image-retry-btn:hover {
    background: var(--primary-dark);
}

/* 婚姻史查询结果样式 */
.marriage-history {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.marriage-history h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.marriage-record {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.marriage-record:last-child {
    margin-bottom: 0;
}

.marriage-record h6 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
}

.marriage-record p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.marriage-record p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 家庭成员查询结果样式 */
.family-members {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--accent-color);
}

.family-members h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.family-member {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
    position: relative;
}

.family-member:last-child {
    margin-bottom: 0;
}

.family-member h6 {
    margin: 0 0 8px 0;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(52, 199, 89, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.family-member p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.family-member p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 家庭成员卡片悬停效果 */
.family-member:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.1);
    transition: all var(--transition-fast);
}

/* 地区姓名猎魔结果样式 */
.hunt-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--warning-color);
}

.hunt-results h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.hunt-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
}

.hunt-count {
    font-weight: 600;
    color: var(--warning-color);
    font-size: 14px;
}

.hunt-time {
    font-size: 12px;
    color: var(--text-tertiary);
}

.hunt-record {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
    position: relative;
}

.hunt-record:last-child {
    margin-bottom: 0;
}

.hunt-record h6 {
    margin: 0 0 8px 0;
    color: var(--warning-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.hunt-record p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.hunt-record p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

.hunt-record .data-source {
    font-size: 12px;
    color: var(--text-tertiary);
    font-style: italic;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--border-light);
}

/* 猎魔记录悬停效果 */
.hunt-record:hover {
    border-color: var(--warning-color);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
    transition: all var(--transition-fast);
}

/* 原始猎魔数据样式 */
.raw-hunt-data {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--text-tertiary);
}

.raw-hunt-data h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.hunt-raw-text {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

/* QQ绑定Phone查询结果样式 */
.qq-phone-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid #1890ff;
}

.qq-phone-results h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.qq-phone-results h5 i {
    color: #1890ff;
    font-size: 18px;
}

.qq-phone-content {
    margin-bottom: 12px;
}

.qq-phone-text {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid rgba(24, 144, 255, 0.1);
}

.qq-phone-results .query-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-tertiary);
    margin-top: 8px;
}

.qq-phone-results .query-time i {
    color: #1890ff;
}

/* 实时定位结果样式 */
.location-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.location-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 定位提示信息 */
.location-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 16px;
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid rgba(255, 149, 0, 0.3);
    border-radius: var(--radius-small);
    color: var(--warning-color);
    font-size: 13px;
}

.location-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

.location-basic,
.location-coordinates,
.location-technical,
.location-device,
.location-time,
.location-map {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.location-basic:last-child,
.location-coordinates:last-child,
.location-technical:last-child,
.location-device:last-child,
.location-time:last-child,
.location-map:last-child {
    margin-bottom: 0;
}

.location-results h6 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.location-results p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.location-results p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 地图链接样式 */
.map-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition-fast);
}

.map-link:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.map-link i {
    font-size: 12px;
}

/* 定位信息卡片悬停效果 */
.location-basic:hover,
.location-coordinates:hover,
.location-technical:hover,
.location-device:hover,
.location-time:hover,
.location-map:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 二要素核验结果样式 */
.verify-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
}

.verify-results.verify-success {
    border-left: 4px solid var(--accent-color);
}

.verify-results.verify-failed {
    border-left: 4px solid var(--error-color);
}

.verify-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 核验状态显示 */
.verify-status {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: var(--radius-medium);
    font-size: 18px;
    font-weight: 600;
}

.verify-success .verify-status {
    background: rgba(52, 199, 89, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(52, 199, 89, 0.3);
}

.verify-failed .verify-status {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 59, 48, 0.3);
}

.verify-status i {
    font-size: 24px;
}

.verify-text {
    font-size: 18px;
}

/* 核验详情 */
.verify-details {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.verify-details h6 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-small);
    display: inline-block;
}

.verify-details p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.verify-details p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 核验提示 */
.verify-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    border-radius: var(--radius-small);
    color: var(--primary-color);
    font-size: 13px;
}

.verify-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 核验结果悬停效果 */
.verify-details:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 图片结果样式（白底个户、档案个户） */
.image-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--accent-color);
}

.image-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 图片容器 */
.image-container {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
    margin-bottom: 16px;
    text-align: center;
}

.household-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-small);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: var(--transition-fast);
}

.household-image:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 图片操作按钮 */
.image-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
}

.image-view-btn,
.image-download-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.image-view-btn {
    background: var(--primary-color);
    color: white;
}

.image-view-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.image-download-btn {
    background: var(--accent-color);
    color: white;
}

.image-download-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.image-view-btn i,
.image-download-btn i {
    font-size: 12px;
}

/* 图片提示 */
.image-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid rgba(255, 149, 0, 0.3);
    border-radius: var(--radius-small);
    color: var(--warning-color);
    font-size: 13px;
}

.image-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 响应式图片操作 */
@media (max-width: 480px) {
    .image-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .image-view-btn,
    .image-download-btn {
        justify-content: center;
    }
}

/* 智慧机主查询结果样式 */
.owner-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.owner-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.owner-basic,
.owner-location {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.owner-basic:last-child,
.owner-location:last-child {
    margin-bottom: 0;
}

.owner-results h6 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.owner-results p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.owner-results p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 机主查询卡片悬停效果 */
.owner-basic:hover,
.owner-location:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 身份证库补齐结果样式 */
.idcard-complete-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--accent-color);
}

.idcard-complete-results h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.idcard-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: rgba(52, 199, 89, 0.1);
    border-radius: var(--radius-small);
}

.idcard-count {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 14px;
}

.idcard-record {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
    position: relative;
}

.idcard-record:last-child {
    margin-bottom: 0;
}

.idcard-record h6 {
    margin: 0 0 8px 0;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(52, 199, 89, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.idcard-record p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.idcard-record p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 身份证记录悬停效果 */
.idcard-record:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.1);
    transition: all var(--transition-fast);
}

/* 原始身份证补齐数据样式 */
.raw-idcard-data {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--text-tertiary);
}

.raw-idcard-data h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.idcard-raw-text {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

/* 手机号状态检测结果样式 */
.phone-status-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
}

.phone-status-results.status-active {
    border-left: 4px solid var(--accent-color);
}

.phone-status-results.status-inactive {
    border-left: 4px solid var(--error-color);
}

.phone-status-results.status-unknown {
    border-left: 4px solid var(--warning-color);
}

.phone-status-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.phone-status-info {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.phone-status-info h6 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-small);
    display: inline-block;
}

.phone-status-info p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.phone-status-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 状态显示 */
.status-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    border-radius: var(--radius-small);
    font-weight: 600;
}

.status-active .status-display {
    background: rgba(52, 199, 89, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(52, 199, 89, 0.3);
}

.status-inactive .status-display {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 59, 48, 0.3);
}

.status-unknown .status-display {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 149, 0, 0.3);
}

.status-display i {
    font-size: 16px;
}

.status-text {
    font-size: 16px;
}

/* 状态提示 */
.status-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    border-radius: var(--radius-small);
    color: var(--primary-color);
    font-size: 13px;
}

.status-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 手机号状态卡片悬停效果 */
.phone-status-info:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 原始手机号状态数据样式 */
.raw-phone-status-data {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--text-tertiary);
}

.raw-phone-status-data h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.phone-status-raw-text {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
}

/* 卡泡聆听音频结果样式 */
.audio-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.audio-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 音频容器 */
.audio-container {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.audio-player {
    text-align: center;
    margin-bottom: 16px;
}

.audio-element {
    width: 100%;
    max-width: 400px;
    height: 40px;
    border-radius: var(--radius-small);
    outline: none;
}

.audio-element::-webkit-media-controls-panel {
    background-color: var(--bg-secondary);
}

.audio-info {
    padding-top: 12px;
    border-top: 1px solid var(--border-light);
}

.audio-info p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
    word-break: break-all;
}

.audio-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

.audio-url {
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

/* 音频操作按钮 */
.audio-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
}

.audio-open-btn,
.audio-download-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.audio-open-btn {
    background: var(--primary-color);
    color: white;
}

.audio-open-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.audio-download-btn {
    background: var(--accent-color);
    color: white;
}

.audio-download-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.audio-open-btn i,
.audio-download-btn i {
    font-size: 12px;
}

/* 音频提示 */
.audio-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    border-radius: var(--radius-small);
    color: var(--primary-color);
    font-size: 13px;
}

.audio-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 音频容器悬停效果 */
.audio-container:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 响应式音频操作 */
@media (max-width: 480px) {
    .audio-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .audio-open-btn,
    .audio-download-btn {
        justify-content: center;
    }

    .audio-element {
        max-width: 100%;
    }
}

/* 综合社工查询结果样式 */
.social-query-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--warning-color);
}

.social-query-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 查询摘要 */
.social-summary {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 16px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.social-summary h6 {
    margin: 0 0 8px 0;
    color: var(--warning-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.social-summary p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.social-summary p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
    display: inline-block;
}

/* 数据块样式 */
.social-section {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.social-section:last-child {
    margin-bottom: 0;
}

.social-section h6 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.social-section h6 i {
    font-size: 12px;
}

/* 不同类型数据块的颜色 */
.section-data-label h6 {
    color: var(--primary-color);
}

.section-data-label {
    border-left: 3px solid var(--primary-color);
}

.section-smart-analysis h6 {
    color: var(--accent-color);
}

.section-smart-analysis {
    border-left: 3px solid var(--accent-color);
}

.section-related-info h6 {
    color: var(--warning-color);
}

.section-related-info {
    border-left: 3px solid var(--warning-color);
}

.section-other h6 {
    color: var(--text-secondary);
}

.section-other {
    border-left: 3px solid var(--text-tertiary);
}

/* 数据块内容 */
.section-content {
    padding-left: 18px;
}

.section-content p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.section-content p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 社工查询提示 */
.social-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid rgba(255, 149, 0, 0.3);
    border-radius: var(--radius-small);
    color: var(--warning-color);
    font-size: 13px;
    margin-top: 16px;
}

.social-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 数据块悬停效果 */
.social-section:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

.social-summary:hover {
    border-color: var(--warning-color);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
    transition: all var(--transition-fast);
}

/* 原始社工查询数据样式 */
.raw-social-data {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--text-tertiary);
}

.raw-social-data h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.social-raw-text {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

/* 网红信息猎魔结果样式 */
.influencer-hunter-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--error-color);
}

.influencer-hunter-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 网红查询摘要 */
.influencer-summary {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 16px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.influencer-summary h6 {
    margin: 0 0 8px 0;
    color: var(--error-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 59, 48, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.influencer-summary p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.influencer-summary p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 100px;
    display: inline-block;
}

/* 网红信息卡片 */
.influencer-card {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
    border-left: 3px solid var(--error-color);
}

.influencer-card:last-child {
    margin-bottom: 0;
}

.influencer-card h6 {
    margin: 0 0 8px 0;
    color: var(--error-color);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.influencer-card h6 i {
    font-size: 12px;
    color: var(--error-color);
}

/* 网红信息内容 */
.influencer-content {
    padding-left: 18px;
}

.influencer-content p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.influencer-content p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

.influencer-content a {
    color: var(--primary-color);
    text-decoration: none;
    word-break: break-all;
}

.influencer-content a:hover {
    text-decoration: underline;
}

/* 网红信息提示 */
.influencer-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid rgba(255, 59, 48, 0.3);
    border-radius: var(--radius-small);
    color: var(--error-color);
    font-size: 13px;
    margin-top: 16px;
}

.influencer-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 网红信息卡片悬停效果 */
.influencer-card:hover {
    border-color: var(--error-color);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.1);
    transition: all var(--transition-fast);
}

.influencer-summary:hover {
    border-color: var(--error-color);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.1);
    transition: all var(--transition-fast);
}

/* 身份证正反面查询结果样式 */
.idcard-dual-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--warning-color);
}

.idcard-dual-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 身份证图片容器 */
.idcard-images-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 16px;
}

.idcard-image-section {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.idcard-image-section h6 {
    margin: 0 0 12px 0;
    color: var(--warning-color);
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    padding: 6px 12px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
}

.idcard-image {
    width: 100%;
    height: auto;
    border-radius: var(--radius-small);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-fast);
}

.idcard-image:hover {
    transform: scale(1.02);
}

/* 身份证图片操作按钮 */
.idcard-image-section .image-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    justify-content: center;
}

.idcard-image-section .image-view-btn,
.idcard-image-section .image-download-btn {
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.idcard-image-section .image-view-btn {
    background: var(--primary-color);
    color: white;
}

.idcard-image-section .image-view-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
}

.idcard-image-section .image-download-btn {
    background: var(--warning-color);
    color: white;
}

.idcard-image-section .image-download-btn:hover {
    background: #E6A500;
    transform: translateY(-1px);
}

/* 身份证提示 */
.idcard-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid rgba(255, 149, 0, 0.3);
    border-radius: var(--radius-small);
    color: var(--warning-color);
    font-size: 13px;
}

.idcard-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 身份证图片区域悬停效果 */
.idcard-image-section:hover {
    border-color: var(--warning-color);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .idcard-images-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .idcard-image-section .image-actions {
        flex-direction: column;
        gap: 8px;
    }

    .idcard-image-section .image-view-btn,
    .idcard-image-section .image-download-btn {
        flex: none;
    }
}

/* 新版照妖镜结果样式 */
.demon-mirror-create-results,
.demon-mirror-view-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.demon-mirror-create-results h5,
.demon-mirror-view-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 临时空间信息 */
.demon-space-info {
    background: var(--bg-primary);
    padding: 16px;
    margin-bottom: 16px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.demon-space-info h6 {
    margin: 0 0 12px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.space-item {
    margin-bottom: 12px;
}

.space-item:last-child {
    margin-bottom: 0;
}

.space-item label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
}

/* 链接容器 */
.link-container,
.password-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.demon-link-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-small);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 12px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.demon-password {
    flex: 1;
    padding: 8px 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
}

.copy-btn {
    padding: 8px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-small);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.copy-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.copy-btn i {
    margin-right: 4px;
}

/* 使用说明 */
.demon-usage-guide {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 16px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.demon-usage-guide h6 {
    margin: 0 0 8px 0;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
}

.demon-usage-guide ol {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.demon-usage-guide li {
    margin-bottom: 4px;
}

/* 拍摄统计 */
.demon-stats {
    background: var(--bg-primary);
    padding: 12px;
    margin-bottom: 16px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.demon-stats p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.demon-stats p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* 拍摄图片网格 */
.demon-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.demon-image-item {
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
}

.demon-image-item h6 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    text-align: center;
}

.demon-image {
    width: 100%;
    height: auto;
    border-radius: var(--radius-small);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-fast);
}

.demon-image:hover {
    transform: scale(1.02);
}

.demon-image-item .image-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.demon-image-item .image-view-btn,
.demon-image-item .image-download-btn {
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 8px;
    border-radius: var(--radius-small);
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.demon-image-item .image-view-btn {
    background: var(--primary-color);
    color: white;
}

.demon-image-item .image-view-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
}

.demon-image-item .image-download-btn {
    background: var(--accent-color);
    color: white;
}

.demon-image-item .image-download-btn:hover {
    background: #2ECC71;
}

/* 照妖镜提示 */
.demon-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    border-radius: var(--radius-small);
    color: var(--primary-color);
    font-size: 13px;
}

.demon-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 照妖镜批量操作按钮 */
.demon-actions {
    display: flex;
    gap: 12px;
    margin: 16px 0;
    flex-wrap: wrap;
}

.demon-download-all-btn,
.demon-copy-all-btn {
    flex: 1;
    min-width: 150px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--radius-small);
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
    text-decoration: none;
}

.demon-download-all-btn {
    background: var(--accent-color);
    color: white;
}

.demon-download-all-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
}

.demon-copy-all-btn {
    background: var(--primary-color);
    color: white;
}

.demon-copy-all-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 图片加载错误样式 */
.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    background: var(--bg-secondary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-small);
    color: var(--text-secondary);
    font-size: 14px;
    text-align: center;
}

.image-error i {
    font-size: 24px;
    margin-bottom: 8px;
    color: #e74c3c;
}

/* 悬停效果 */
.demon-space-info:hover,
.demon-stats:hover,
.demon-usage-guide:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

.demon-image-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demon-images-grid {
        grid-template-columns: 1fr;
    }

    .link-container,
    .password-container {
        flex-direction: column;
        align-items: stretch;
    }

    .demon-link-input {
        margin-bottom: 8px;
    }

    .demon-password {
        margin-bottom: 8px;
    }

    /* 移动端批量操作按钮 */
    .demon-actions {
        flex-direction: column;
        gap: 8px;
    }

    .demon-download-all-btn,
    .demon-copy-all-btn {
        min-width: auto;
        width: 100%;
    }
}

/* 二维码生成结果样式 */
.qrcode-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--accent-color);
}

.qrcode-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 二维码容器 */
.qrcode-container {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 20px;
    margin-bottom: 16px;
    align-items: start;
}

.qrcode-image-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    padding: 16px;
    border-radius: var(--radius-medium);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qrcode-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-small);
}

.qrcode-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    min-width: 200px;
}

.qrcode-placeholder canvas {
    border-radius: var(--radius-small);
}

.qr-error {
    color: var(--error-color);
    text-align: center;
    padding: 20px;
    font-size: 14px;
}

/* 二维码信息 */
.qrcode-info {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.qrcode-info h6 {
    margin: 0 0 12px 0;
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(52, 199, 89, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.qrcode-info p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.qrcode-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

.qrcode-content {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--border-light);
}

.qrcode-content strong {
    color: var(--text-secondary);
    font-weight: 500;
    display: block;
    margin-bottom: 6px;
}

.content-display {
    background: var(--bg-tertiary);
    padding: 8px 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    color: var(--text-primary);
    word-break: break-all;
    max-height: 100px;
    overflow-y: auto;
}

/* 二维码操作按钮 */
.qrcode-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.qrcode-view-btn,
.qrcode-download-btn,
.qrcode-copy-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.qrcode-view-btn {
    background: var(--primary-color);
    color: white;
}

.qrcode-view-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.qrcode-download-btn {
    background: var(--accent-color);
    color: white;
}

.qrcode-download-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.qrcode-copy-btn {
    background: var(--warning-color);
    color: white;
}

.qrcode-copy-btn:hover {
    background: #E6A500;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.qrcode-view-btn i,
.qrcode-download-btn i,
.qrcode-copy-btn i {
    font-size: 12px;
}

/* 二维码提示 */
.qrcode-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(52, 199, 89, 0.1);
    border: 1px solid rgba(52, 199, 89, 0.3);
    border-radius: var(--radius-small);
    color: var(--accent-color);
    font-size: 13px;
}

.qrcode-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 二维码容器悬停效果 */
.qrcode-info:hover {
    border-color: var(--accent-color);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.1);
    transition: all var(--transition-fast);
}

.qrcode-image-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .qrcode-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .qrcode-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .qrcode-view-btn,
    .qrcode-download-btn,
    .qrcode-copy-btn {
        justify-content: center;
    }
}

/* 买家秀欣赏结果样式 */
.buyer-show-results,
.buyer-show-json-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--warning-color);
}

.buyer-show-results h5,
.buyer-show-json-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* 买家秀图片容器 */
.buyer-show-container {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 20px;
    margin-bottom: 16px;
    align-items: start;
}

.buyer-show-image-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background: white;
    padding: 12px;
    border-radius: var(--radius-medium);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 300px;
}

.buyer-show-image {
    max-width: 100%;
    max-height: 400px;
    height: auto;
    border-radius: var(--radius-small);
    transition: transform var(--transition-fast);
}

.buyer-show-image:hover {
    transform: scale(1.02);
}

/* 买家秀信息 */
.buyer-show-info {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.buyer-show-info h6 {
    margin: 0 0 12px 0;
    color: var(--warning-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.buyer-show-info p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.buyer-show-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 60px;
    display: inline-block;
}

/* JSON数据容器 */
.buyer-show-json-container {
    background: var(--bg-primary);
    padding: 16px;
    margin-bottom: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.json-info {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-light);
}

.json-info h6 {
    margin: 0 0 8px 0;
    color: var(--warning-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.json-info p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.json-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 60px;
    display: inline-block;
}

.json-content h6 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
}

.json-display {
    background: var(--bg-tertiary);
    padding: 12px;
    border-radius: var(--radius-small);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    color: var(--text-primary);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

/* 买家秀操作按钮 */
.buyer-show-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.buyer-show-view-btn,
.buyer-show-download-btn,
.buyer-show-refresh-btn,
.buyer-show-copy-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.buyer-show-view-btn {
    background: var(--primary-color);
    color: white;
}

.buyer-show-view-btn:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.buyer-show-download-btn {
    background: var(--accent-color);
    color: white;
}

.buyer-show-download-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.buyer-show-refresh-btn {
    background: var(--warning-color);
    color: white;
}

.buyer-show-refresh-btn:hover {
    background: #E6A500;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.buyer-show-copy-btn {
    background: var(--text-tertiary);
    color: white;
}

.buyer-show-copy-btn:hover {
    background: var(--text-secondary);
    transform: translateY(-1px);
}

.buyer-show-view-btn i,
.buyer-show-download-btn i,
.buyer-show-refresh-btn i,
.buyer-show-copy-btn i {
    font-size: 12px;
}

/* 买家秀提示 */
.buyer-show-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 149, 0, 0.1);
    border: 1px solid rgba(255, 149, 0, 0.3);
    border-radius: var(--radius-small);
    color: var(--warning-color);
    font-size: 13px;
}

.buyer-show-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* 买家秀容器悬停效果 */
.buyer-show-info:hover,
.buyer-show-json-container:hover {
    border-color: var(--warning-color);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.1);
    transition: all var(--transition-fast);
}

.buyer-show-image-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .buyer-show-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .buyer-show-image-section {
        max-width: 100%;
    }

    .buyer-show-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .buyer-show-view-btn,
    .buyer-show-download-btn,
    .buyer-show-refresh-btn,
    .buyer-show-copy-btn {
        justify-content: center;
    }
}

/* IP信息查询结果样式 */
.ip-query-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--primary-color);
}

.ip-query-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* IP信息容器 */
.ip-info-container {
    display: grid;
    gap: 16px;
    margin-bottom: 16px;
}

.ip-basic-info,
.ip-location-info,
.ip-network-info {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.ip-basic-info h6,
.ip-location-info h6,
.ip-network-info h6 {
    margin: 0 0 12px 0;
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

/* IP信息网格 */
.ip-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
    min-width: 60px;
    flex-shrink: 0;
}

.info-item span {
    color: var(--text-primary);
    font-size: 14px;
    word-break: break-all;
}

.ip-address {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(0, 122, 255, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-small);
}

/* 地图链接 */
.map-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: var(--accent-color);
    text-decoration: none;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: var(--radius-small);
    background: rgba(52, 199, 89, 0.1);
    transition: var(--transition-fast);
}

.map-link:hover {
    background: rgba(52, 199, 89, 0.2);
    text-decoration: none;
    color: var(--accent-color);
    transform: translateY(-1px);
}

.map-link i {
    font-size: 12px;
}

/* IP操作按钮 */
.ip-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.ip-copy-btn,
.ip-query-btn,
.ip-location-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.ip-copy-btn {
    background: var(--text-tertiary);
    color: white;
}

.ip-copy-btn:hover {
    background: var(--text-secondary);
    transform: translateY(-1px);
}

.ip-query-btn {
    background: var(--primary-color);
    color: white;
}

.ip-query-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.ip-location-btn {
    background: var(--accent-color);
    color: white;
}

.ip-location-btn:hover {
    background: #2ECC71;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.ip-copy-btn i,
.ip-query-btn i,
.ip-location-btn i {
    font-size: 12px;
}

/* IP查询提示 */
.ip-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(0, 122, 255, 0.1);
    border: 1px solid rgba(0, 122, 255, 0.3);
    border-radius: var(--radius-small);
    color: var(--primary-color);
    font-size: 13px;
}

.ip-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* IP信息容器悬停效果 */
.ip-basic-info:hover,
.ip-location-info:hover,
.ip-network-info:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ip-info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .info-item label {
        min-width: auto;
    }

    .ip-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .ip-copy-btn,
    .ip-query-btn,
    .ip-location-btn {
        justify-content: center;
    }
}

/* LSP每日自律结果样式 */
.lsp-daily-results,
.lsp-list-results {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-medium);
    border-left: 4px solid var(--error-color);
}

.lsp-daily-results h5,
.lsp-list-results h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

/* LSP视频容器 */
.lsp-video-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 16px;
    align-items: start;
}

.lsp-video-player {
    background: #000;
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.lsp-video {
    width: 100%;
    height: auto;
    min-height: 200px;
    background: #000;
}

/* LSP视频信息 */
.lsp-video-info {
    background: var(--bg-primary);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.lsp-video-info h6 {
    margin: 0 0 12px 0;
    color: var(--error-color);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
    background: rgba(255, 59, 48, 0.1);
    border-radius: var(--radius-small);
    display: inline-block;
}

.video-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.video-details p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

/* LSP视频列表 */
.lsp-list-info {
    background: var(--bg-primary);
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.lsp-list-info p {
    margin: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.lsp-list-info p strong {
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 80px;
    display: inline-block;
}

.lsp-video-list {
    display: grid;
    gap: 12px;
    margin-bottom: 16px;
}

.lsp-video-item {
    display: flex;
    align-items: center;
    gap: 16px;
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.lsp-video-item:hover {
    border-color: var(--error-color);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.1);
}

.video-item-info {
    flex: 1;
}

.video-item-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.video-url {
    margin: 0;
    font-size: 12px;
    color: var(--text-tertiary);
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.video-item-actions {
    display: flex;
    gap: 8px;
}

.video-item-actions .play-btn,
.video-item-actions .copy-btn {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-small);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.video-item-actions .play-btn {
    background: var(--error-color);
    color: white;
}

.video-item-actions .play-btn:hover {
    background: #D70015;
    transform: translateY(-1px);
}

.video-item-actions .copy-btn {
    background: var(--text-tertiary);
    color: white;
}

.video-item-actions .copy-btn:hover {
    background: var(--text-secondary);
    transform: translateY(-1px);
}

/* LSP操作按钮 */
.lsp-actions,
.lsp-list-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.lsp-refresh-btn,
.lsp-copy-btn,
.lsp-external-btn,
.lsp-random-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: var(--radius-small);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
}

.lsp-refresh-btn {
    background: var(--warning-color);
    color: white;
}

.lsp-refresh-btn:hover {
    background: #E6A500;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.lsp-copy-btn {
    background: var(--text-tertiary);
    color: white;
}

.lsp-copy-btn:hover {
    background: var(--text-secondary);
    transform: translateY(-1px);
}

.lsp-external-btn {
    background: var(--primary-color);
    color: white;
}

.lsp-external-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.lsp-random-btn {
    background: var(--error-color);
    color: white;
}

.lsp-random-btn:hover {
    background: #D70015;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
}

.lsp-refresh-btn i,
.lsp-copy-btn i,
.lsp-external-btn i,
.lsp-random-btn i {
    font-size: 12px;
}

/* LSP提示 */
.lsp-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid rgba(255, 59, 48, 0.3);
    border-radius: var(--radius-small);
    color: var(--error-color);
    font-size: 13px;
}

.lsp-notice i {
    font-size: 14px;
    flex-shrink: 0;
}

/* LSP视频弹窗 */
.lsp-video-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.lsp-video-player-popup {
    background: var(--bg-primary);
    border-radius: var(--radius-medium);
    overflow: hidden;
    max-width: 90vw;
    max-height: 90vh;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.video-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.video-popup-header h6 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.video-popup-header .close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-small);
    transition: var(--transition-fast);
}

.video-popup-header .close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* LSP容器悬停效果 */
.lsp-video-info:hover,
.lsp-list-info:hover {
    border-color: var(--error-color);
    box-shadow: 0 2px 8px rgba(255, 59, 48, 0.1);
    transition: all var(--transition-fast);
}

.lsp-video-player:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
    transition: all var(--transition-fast);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .lsp-video-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .lsp-actions,
    .lsp-list-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .lsp-refresh-btn,
    .lsp-copy-btn,
    .lsp-external-btn,
    .lsp-random-btn {
        justify-content: center;
    }

    .lsp-video-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .video-item-actions {
        justify-content: center;
    }

    .lsp-video-player-popup {
        max-width: 95vw;
        max-height: 95vh;
    }
}

/* 右上角悬浮会员竖幅 */
.floating-vip-banner {
    position: fixed;
    top: 80px;
    right: 0;
    transform: translateX(0);
    width: 120px;
    height: 200px;
    background: linear-gradient(135deg, #FF6B35, #F7931E, #FFD700);
    border-radius: 12px 0 0 12px;
    z-index: 1000;
    overflow: hidden;
    transition: all var(--transition-medium);
    box-shadow: -4px 0 20px rgba(255, 107, 53, 0.3);
    animation: bannerPulse 3s ease-in-out infinite;
    opacity: 1;
    visibility: visible;
}

.floating-vip-banner.minimized {
    opacity: 0;
    visibility: hidden;
    transform: translateX(100%);
}

.floating-vip-banner:hover {
    transform: translateX(-10px);
    box-shadow: -8px 0 30px rgba(255, 107, 53, 0.5);
    animation: none;
}

/* 最小化按钮 */
.banner-minimize {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    cursor: pointer;
    z-index: 3;
    transition: all var(--transition-fast);
    opacity: 0.7;
}

.banner-minimize:hover {
    background: rgba(0, 0, 0, 0.6);
    opacity: 1;
    transform: scale(1.1);
}

.floating-vip-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    animation: bannerShine 4s ease-in-out infinite;
}

.banner-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
    color: white;
    text-align: center;
    z-index: 2;
    cursor: pointer;
}

/* 最小化后的小图标 */
.floating-vip-mini {
    position: fixed;
    top: 80px;
    right: 10px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    transition: all var(--transition-medium);
    opacity: 0;
    visibility: hidden;
    transform: scale(0.8);
}

.floating-vip-mini.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.floating-vip-mini:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
}

.mini-pulse {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #FF3B30;
    border-radius: 50%;
    animation: miniPulse 2s ease-in-out infinite;
}

.banner-icon {
    font-size: 24px;
    margin-bottom: 12px;
    animation: bannerIconBounce 2s ease-in-out infinite;
}

.banner-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 4px;
}

.banner-title {
    font-size: 16px;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.banner-subtitle {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.95;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.banner-arrow {
    font-size: 12px;
    margin-top: 8px;
    opacity: 0.8;
    animation: bannerArrowMove 1.5s ease-in-out infinite;
}

.banner-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: bannerGlow 3s ease-in-out infinite;
    pointer-events: none;
}

/* 悬浮竖幅动画 */
@keyframes bannerPulse {
    0%, 100% {
        transform: translateX(0) scale(1);
    }
    50% {
        transform: translateX(0) scale(1.02);
    }
}

/* 移动端横幅脉动动画 */
@media (max-width: 768px) {
    @keyframes bannerPulse {
        0%, 100% {
            transform: translateX(-50%) scale(1);
        }
        50% {
            transform: translateX(-50%) scale(1.02);
        }
    }
}

@keyframes bannerShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes bannerIconBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

@keyframes bannerArrowMove {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(2px);
    }
}

@keyframes bannerGlow {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg);
    }
    50% {
        opacity: 0.6;
        transform: rotate(180deg);
    }
}

@keyframes miniPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* 响应式设计 - 悬浮竖幅 */
@media (max-width: 768px) {
    /* 移动端改为底部横幅 */
    .floating-vip-banner {
        top: auto;
        bottom: 80px; /* 在底栏上方 */
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 280px;
        height: 60px;
        border-radius: 30px;
        flex-direction: row;
        box-shadow: 0 4px 20px rgba(255, 107, 53, 0.4);
    }

    .floating-vip-banner.minimized {
        transform: translateX(-50%) translateY(100px);
    }

    .floating-vip-banner:hover {
        transform: translateX(-50%) translateY(-5px);
    }

    .banner-content {
        flex-direction: row;
        padding: 12px 20px;
        gap: 12px;
        height: 100%;
        align-items: center;
        justify-content: space-between;
    }

    .banner-icon {
        font-size: 24px;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .banner-text {
        flex: 1;
        gap: 2px;
        text-align: left;
    }

    .banner-title {
        font-size: 16px;
        line-height: 1.2;
    }

    .banner-subtitle {
        font-size: 12px;
        line-height: 1.2;
    }

    .banner-arrow {
        font-size: 14px;
        margin-top: 0;
        flex-shrink: 0;
    }

    .banner-minimize {
        bottom: 5px;
        right: 5px;
        width: 28px;
        height: 28px;
        font-size: 12px;
        /* 增大触摸区域 */
        padding: 4px;
        background: rgba(0, 0, 0, 0.5);
    }

    /* 移动端触摸优化 */
    .banner-minimize:active {
        background: rgba(0, 0, 0, 0.8);
        transform: scale(0.95);
    }

    /* 移动端小图标调整 */
    .floating-vip-mini {
        bottom: 90px;
        left: 50%;
        right: auto;
        top: auto;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .floating-vip-mini.show {
        transform: translateX(-50%) scale(1);
    }

    .floating-vip-mini:hover {
        transform: translateX(-50%) scale(1.1);
    }
}

@media (max-width: 480px) {
    .floating-vip-banner {
        width: 260px;
        height: 55px;
        bottom: 75px;
    }

    .banner-content {
        padding: 10px 16px;
        gap: 10px;
    }

    .banner-icon {
        font-size: 20px;
    }

    .banner-title {
        font-size: 14px;
    }

    .banner-subtitle {
        font-size: 11px;
    }

    .banner-arrow {
        font-size: 12px;
    }

    .floating-vip-mini {
        width: 50px;
        height: 50px;
        font-size: 20px;
        bottom: 85px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
    .floating-vip-banner {
        width: 240px;
        height: 50px;
        bottom: 70px;
    }

    .banner-content {
        padding: 8px 12px;
        gap: 8px;
    }

    .banner-icon {
        font-size: 18px;
    }

    .banner-title {
        font-size: 13px;
    }

    .banner-subtitle {
        font-size: 10px;
    }

    .banner-arrow {
        font-size: 11px;
    }
}

/* 避免与其他悬浮元素冲突 */
@media (max-height: 600px) {
    .floating-vip-banner {
        top: 60px;
        height: 160px;
    }
}

/* 确保不与导航栏重叠 */
@media (max-width: 768px) {
    .floating-vip-banner {
        top: 70px; /* 移动端导航栏高度调整 */
    }
}

.result-text {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    white-space: pre-wrap;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 充值模态框样式 */
.recharge-content {
    max-width: 100%;
}

.vip-benefits {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    padding: 20px;
    border-radius: var(--radius-medium);
    margin-bottom: 20px;
    color: #333;
}

.vip-benefits h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-weight: 600;
}

.vip-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vip-benefits li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.vip-benefits li i {
    color: #28a745;
    font-weight: bold;
}

.recharge-methods h4 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.method-card {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    margin-bottom: 15px;
    overflow: hidden;
    transition: var(--transition-fast);
}

.method-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.method-header {
    background: var(--bg-secondary);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--text-primary);
}

.method-content {
    padding: 20px;
}

.method-content p {
    margin: 0 0 15px 0;
    color: var(--text-secondary);
}

.recharge-result {
    margin-top: 20px;
    padding: 20px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color);
}

.recharge-result.success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.recharge-result.error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.btn-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-success:hover::before {
    left: 100%;
}

/* Telegram按钮特殊样式 */
.btn-telegram {
    background: linear-gradient(135deg, #0088cc, #229ED9);
    color: white;
    border: none;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.btn-telegram:hover {
    background: linear-gradient(135deg, #006699, #1a7db8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3);
}

.btn-telegram::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-telegram:hover::before {
    left: 100%;
}

/* 购买按钮容器 */
.purchase-buttons {
    display: flex;
    gap: 12px;
    flex-direction: column;
}

/* 在线商城按钮 */
.btn-shop {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-shop::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-shop:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-shop:hover::before {
    left: 100%;
}

/* 响应式购买按钮 */
@media (min-width: 480px) {
    .purchase-buttons {
        flex-direction: row;
    }

    .purchase-buttons .btn {
        flex: 1;
    }
}

/* 公告栏样式 */
.announcement-banner {
    margin-bottom: 32px;
    position: relative;
}

.announcement-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    overflow: hidden;
    position: relative;
}

.announcement-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: bannerShimmer 4s ease-in-out infinite;
}

.announcement-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.announcement-icon {
    font-size: 24px;
    color: #FFD700;
    margin-right: 12px;
    animation: iconPulse 2s ease-in-out infinite;
}

.announcement-title {
    flex: 1;
    margin: 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.announcement-toggle {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.announcement-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.1);
}

.announcement-toggle.collapsed i {
    transform: rotate(180deg);
}

.announcement-content {
    padding: 24px;
    color: white;
    transition: all 0.3s ease;
    max-height: 1000px;
    overflow: hidden;
}

.announcement-content.collapsed {
    max-height: 0;
    padding: 0 24px;
}

/* 公告消息 */
.announcement-messages {
    margin-bottom: 24px;
}

.message-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
}

.message-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(8px);
}

.message-item:last-child {
    margin-bottom: 0;
}

.message-icon {
    font-size: 20px;
    margin-right: 12px;
    animation: messageIconBounce 3s ease-in-out infinite;
}

.message-text {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    font-weight: 500;
}

/* 操作按钮区域 */
.announcement-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: white;
}

.action-btn i {
    font-size: 16px;
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.action-btn:hover .btn-shine {
    left: 100%;
}

.qq-group-btn {
    background: linear-gradient(135deg, #12B7F5, #0E9FE0);
    box-shadow: 0 4px 15px rgba(18, 183, 245, 0.4);
}

.qq-group-btn:hover {
    background: linear-gradient(135deg, #0E9FE0, #12B7F5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(18, 183, 245, 0.5);
}

.api-doc-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.api-doc-btn:hover {
    background: linear-gradient(135deg, #20c997, #28a745);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.5);
}

.recharge-btn-announce {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.recharge-btn-announce:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
}

.vip-btn-announce {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.vip-btn-announce:hover {
    background: linear-gradient(135deg, #f5576c, #f093fb);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 147, 251, 0.5);
}

/* 充值激励区域 */
.recharge-incentive {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.15));
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.recharge-incentive::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: incentiveRotate 8s linear infinite;
}

.incentive-content {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 1;
}

.incentive-icon {
    font-size: 32px;
    animation: incentiveIconFloat 3s ease-in-out infinite;
}

.incentive-text {
    flex: 1;
}

.incentive-text h3 {
    margin: 0 0 8px 0;
    color: #FFD700;
    font-size: 18px;
    font-weight: 700;
}

.incentive-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    line-height: 1.4;
}

.incentive-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #333;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.incentive-btn:hover {
    background: linear-gradient(135deg, #FFA500, #FFD700);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

/* 动画效果 */
@keyframes bannerShimmer {
    0%, 100% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes messageIconBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

@keyframes incentiveRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes incentiveIconFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .announcement-header {
        padding: 16px 20px;
    }

    .announcement-title {
        font-size: 18px;
    }

    .announcement-content {
        padding: 20px;
    }

    .announcement-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .action-btn {
        padding: 12px 16px;
        font-size: 13px;
    }

    .incentive-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .incentive-text h3 {
        font-size: 16px;
    }

    .incentive-text p {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .announcement-actions {
        grid-template-columns: 1fr;
    }

    .message-item {
        padding: 10px 12px;
    }

    .message-text {
        font-size: 14px;
    }

    .recharge-incentive {
        padding: 16px;
    }
}
