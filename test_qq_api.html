<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ绑定Phone查询API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ绑定Phone查询API测试</h1>
        <p>这是一个测试页面，用于验证QQ绑定Phone查询API的功能。</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="token">Token:</label>
                <input type="text" id="token" name="token" placeholder="请输入API Token" required>
            </div>
            
            <div class="form-group">
                <label for="qq">QQ号:</label>
                <input type="text" id="qq" name="qq" placeholder="请输入5-11位QQ号码" required>
            </div>
            
            <button type="submit">查询</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('token').value.trim();
            const qq = document.getElementById('qq').value.trim();
            const resultDiv = document.getElementById('result');
            
            // 验证输入
            if (!token) {
                showResult('请输入Token', 'error');
                return;
            }
            
            if (!qq) {
                showResult('请输入QQ号', 'error');
                return;
            }
            
            // 验证QQ号格式
            if (!/^\d{5,11}$/.test(qq)) {
                showResult('QQ号格式不正确，请输入5-11位数字', 'error');
                return;
            }
            
            // 显示加载状态
            showResult('正在查询，请稍候...', 'loading');
            
            try {
                // 调用API
                const response = await fetch('./api/qq_phone_query/index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: token,
                        qq: qq
                    })
                });
                
                const result = await response.json();
                
                // 显示结果
                showResult(JSON.stringify(result, null, 2), result.code === 200 ? 'success' : 'error');
                
            } catch (error) {
                showResult('请求失败: ' + error.message, 'error');
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
