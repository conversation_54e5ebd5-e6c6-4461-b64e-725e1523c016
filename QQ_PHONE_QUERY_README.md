# QQ绑定Phone查询功能说明

## 功能概述

QQ绑定Phone查询是一个VIP专享功能，允许用户根据QQ号查询绑定的手机号信息。

## 功能特点

- **VIP专享**: 需要VIP权限才能使用
- **快速查询**: 支持5-11位QQ号码查询
- **详细信息**: 返回QQ号关联的手机号码信息
- **安全可靠**: 使用上游API确保数据准确性

## API接口

### 请求地址
```
POST /api/qq_phone_query/index.php
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | string | 是 | 用户认证Token |
| qq | string | 是 | QQ号码（5-11位数字） |

### 请求示例

```bash
curl -X POST "http://your-domain.com/api/qq_phone_query/index.php" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "your_token_here",
    "qq": "10001"
  }'
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "查询成功",
  "shuju": "QQ: 10001\n查询到以下结果↓:\n\nqq: 10001\n手机号: 13688805189\n",
  "execution_time": "3.4318 秒"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "QQ号格式不正确，请输入5-11位数字"
}
```

## 前端集成

### 工具配置

在 `assets/js/config.js` 中已添加工具配置：

```javascript
{
    id: 'qq_phone_query',
    name: 'QQ绑定Phone查询',
    description: '根据QQ号查询绑定的手机号信息，支持查询QQ关联的手机号码',
    icon: 'fab fa-qq',
    category: 'vip',
    vip: true,
    endpoint: '/api/qq_phone_query/index.php',
    fields: [
        { name: 'qq', label: 'QQ号', type: 'text', required: true, placeholder: '请输入5-11位QQ号码' }
    ]
}
```

### 样式定制

在 `assets/css/style.css` 中添加了专门的样式：

- `.qq-phone-results`: 主容器样式
- `.qq-phone-text`: 结果文本样式
- `.query-time`: 查询时间显示样式

### 验证功能

添加了QQ号格式验证：

```javascript
// 在 Utils 对象中
validateQQ: function(qq) {
    const pattern = /^\d{5,11}$/;
    return pattern.test(qq);
}
```

## 使用方法

1. **登录系统**: 确保已登录并具有VIP权限
2. **选择工具**: 在VIP工具分类中找到"QQ绑定Phone查询"
3. **输入QQ号**: 输入要查询的QQ号码（5-11位数字）
4. **提交查询**: 点击查询按钮
5. **查看结果**: 系统将显示查询结果，包括QQ号和关联的手机号

## 注意事项

1. **权限要求**: 此功能仅限VIP用户使用
2. **格式要求**: QQ号必须是5-11位数字
3. **查询限制**: 受上游API限制，可能有查询频率限制
4. **数据准确性**: 查询结果仅供参考，实际情况以官方为准

## 错误处理

系统会处理以下错误情况：

- 缺少必要参数
- QQ号格式不正确
- Token无效或过期
- 上游API服务不可用
- 网络连接问题

## 测试

可以使用 `test_qq_api.html` 文件进行API功能测试。

## 技术实现

- **后端**: PHP实现，使用cURL进行HTTP请求
- **前端**: JavaScript处理表单验证和结果显示
- **样式**: CSS3实现美观的界面效果
- **验证**: 客户端和服务端双重验证

## 更新日志

- **v1.0.0**: 初始版本，实现基本的QQ绑定Phone查询功能
