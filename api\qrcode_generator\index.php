<?php
/**
 * 二维码生成API
 * 免费功能，支持文本、链接等内容的二维码生成
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $content = $_GET['content'] ?? '';
        $size = $_GET['size'] ?? '200';
        $level = $_GET['level'] ?? 'M';
    } else {
        $content = $input['content'] ?? '';
        $size = $input['size'] ?? '200';
        $level = $input['level'] ?? 'M';
    }
    
    // 基本参数验证
    if (empty($content)) {
        echo json_encode([
            'code' => 400,
            'message' => '请输入要生成二维码的内容'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证内容长度
    if (strlen($content) > 2000) {
        echo json_encode([
            'code' => 400,
            'message' => '内容长度不能超过2000个字符'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证尺寸参数
    $size = intval($size);
    if ($size < 100 || $size > 500) {
        $size = 200; // 默认尺寸
    }
    
    // 验证纠错级别
    if (!in_array($level, ['L', 'M', 'Q', 'H'])) {
        $level = 'M'; // 默认级别
    }
    
    // 分析内容类型
    $contentType = analyzeContentType($content);

    // 返回参数给前端，让前端使用qrcode.js生成
    echo json_encode([
        'code' => 200,
        'message' => '参数验证成功，准备生成二维码',
        'content' => $content,
        'content_type' => $contentType,
        'size' => $size,
        'level' => $level,
        'level_description' => getLevelDescription($level),
        'generate_client_side' => true // 标识前端生成
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}



/**
 * 分析内容类型
 */
function analyzeContentType($content) {
    // URL检测
    if (filter_var($content, FILTER_VALIDATE_URL)) {
        if (strpos($content, 'http://') === 0 || strpos($content, 'https://') === 0) {
            return 'URL链接';
        }
    }
    
    // 邮箱检测
    if (filter_var($content, FILTER_VALIDATE_EMAIL)) {
        return '邮箱地址';
    }
    
    // 电话号码检测
    if (preg_match('/^(\+?86)?1[3-9]\d{9}$/', $content)) {
        return '手机号码';
    }
    
    // WiFi配置检测
    if (strpos($content, 'WIFI:') === 0) {
        return 'WiFi配置';
    }
    
    // 地理位置检测
    if (strpos($content, 'geo:') === 0) {
        return '地理位置';
    }
    
    // 短信检测
    if (strpos($content, 'sms:') === 0 || strpos($content, 'smsto:') === 0) {
        return '短信';
    }
    
    // 电话检测
    if (strpos($content, 'tel:') === 0) {
        return '电话号码';
    }
    
    // 默认为文本
    return '纯文本';
}



/**
 * 获取纠错级别描述
 */
function getLevelDescription($level) {
    $descriptions = [
        'L' => '低级纠错（约7%）',
        'M' => '中级纠错（约15%）',
        'Q' => '高级纠错（约25%）',
        'H' => '最高纠错（约30%）'
    ];

    return $descriptions[$level] ?? '中级纠错（约15%）';
}
?>
