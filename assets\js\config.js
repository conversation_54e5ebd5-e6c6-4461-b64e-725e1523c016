// 应用配置文件
const CONFIG = {
    // API配置
    API: {
        BASE_URL: window.location.origin,
        UPSTREAM_BASE_URL: 'https://api.qnm6.top',
        ENDPOINTS: {
            // 本地API端点
            LOCAL_REGISTER: '/api/auth/register.php',
            LOCAL_LOGIN: '/api/auth/login.php',
            LOCAL_LOGOUT: '/api/auth/logout.php',
            LOCAL_PROFILE: '/api/auth/profile.php',
            
            // 上游API端点
            UPSTREAM_REGISTER: '/admapi/zhuce.php',
            UPSTREAM_USER_INFO: '/admapi/demo.php',
            
            // 功能API端点
            BASIC_INFO_QUERY: '/api/dujia/index.php',
            SOCIAL_MEDIA_QUERY: '/api/dujia/index.php?action=generate_id'
        }
    },
    
    // 应用设置
    APP: {
        NAME: 'iDatas社工系统',
        VERSION: '1.0.0',
        DESCRIPTION: '专业的社工数据查询平台',
        AUTHOR: 'iDatas Team',
        
        // 会话设置
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
        AUTO_LOGOUT_WARNING: 5 * 60 * 1000,   // 5分钟前警告
        
        // UI设置
        TOAST_DURATION: 3000,
        LOADING_MIN_DURATION: 500,
        ANIMATION_DURATION: 300,
        
        // 分页设置
        TOOLS_PER_PAGE: 12,
        SEARCH_DEBOUNCE: 300
    },
    
    // 工具配置
    TOOLS: [
        {
            id: 'qrcode_generator',
            name: '二维码生成',
            description: '免费生成二维码，支持文本、链接、电话、邮箱等多种内容类型',
            icon: 'fas fa-qrcode',
            category: 'basic',
            vip: false,
            endpoint: '/api/qrcode_generator/index.php',
            fields: [
                { name: 'content', label: '二维码内容', type: 'textarea', required: true, placeholder: '请输入要生成二维码的内容（支持文本、链接、电话、邮箱等）' },
                {
                    name: 'size',
                    label: '二维码尺寸',
                    type: 'select',
                    required: false,
                    options: [
                        { value: '150', text: '150x150 像素' },
                        { value: '200', text: '200x200 像素（推荐）' },
                        { value: '250', text: '250x250 像素' },
                        { value: '300', text: '300x300 像素' },
                        { value: '400', text: '400x400 像素' }
                    ]
                },
                {
                    name: 'level',
                    label: '纠错级别',
                    type: 'select',
                    required: false,
                    options: [
                        { value: 'L', text: '低级纠错（约7%）' },
                        { value: 'M', text: '中级纠错（约15%）' },
                        { value: 'Q', text: '高级纠错（约25%）' },
                        { value: 'H', text: '最高纠错（约30%）' }
                    ]
                }
            ]
        },
        {
            id: 'buyer_show',
            name: '买家秀欣赏',
            description: '免费欣赏淘宝买家秀图片，支持获取随机图片和详细数据',
            icon: 'fas fa-camera-retro',
            category: 'basic',
            vip: false,
            endpoint: '/api/buyer_show/index.php',
            fields: [
                {
                    name: 'type',
                    label: '获取类型',
                    type: 'select',
                    required: true,
                    options: [
                        { value: 'image', text: '获取买家秀图片' },
                        { value: 'json', text: '获取详细数据（JSON格式）' }
                    ]
                }
            ]
        },
        {
            id: 'ip_query',
            name: 'IP信息查询',
            description: '免费查询IP地址的地理位置、ISP信息等详细数据，支持IPv4地址',
            icon: 'fas fa-globe-americas',
            category: 'basic',
            vip: false,
            endpoint: '/api/ip_query/index.php',
            fields: [
                { name: 'ip', label: 'IP地址', type: 'text', required: false, placeholder: '请输入IP地址（留空则查询当前IP）' }
            ]
        },
        {
            id: 'lsp_daily',
            name: 'LSP每日自律',
            description: '免费观看自律视频，帮助LSP们每日自律，支持HLS视频播放',
            icon: 'fas fa-video',
            category: 'basic',
            vip: false,
            endpoint: '/api/lsp_daily/index.php',
            fields: [
                {
                    name: 'action',
                    label: '操作类型',
                    type: 'select',
                    required: true,
                    options: [
                        { value: 'get_video', text: '获取随机自律视频' },
                        { value: 'get_list', text: '查看所有视频列表' }
                    ]
                }
            ]
        },
        {
            id: 'civil_lawsuit_query',
            name: '民事诉讼信息查询',
            description: '查询个人民事诉讼相关信息，包括起诉、应诉等记录',
            icon: 'fas fa-gavel',
            category: 'vip',
            vip: true,
            endpoint: '/api/basic_info/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ],
            queryType: '1' // lx参数值
        },
        {
            id: 'household_info_query',
            name: '户籍信息查询',
            description: '查询个人户籍登记信息，包括户口所在地、迁移记录等',
            icon: 'fas fa-home',
            category: 'vip',
            vip: true,
            endpoint: '/api/basic_info/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ],
            queryType: '2' // lx参数值
        },
        {
            id: 'douyin_reverse_query',
            name: '抖音反查',
            description: '通过抖音账号和姓名反查身份证号码信息',
            icon: 'fab fa-tiktok',
            category: 'vip',
            vip: true,
            endpoint: '/api/social_reverse/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'account', label: '抖音账号', type: 'text', required: true, placeholder: '请输入抖音号或昵称' }
            ],
            platform: 'douyin'
        },
        {
            id: 'kuaishou_reverse_query',
            name: '快手反查',
            description: '通过快手账号和姓名反查身份证号码信息',
            icon: 'fas fa-video',
            category: 'vip',
            vip: true,
            endpoint: '/api/social_reverse/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'account', label: '快手账号', type: 'text', required: true, placeholder: '请输入快手号或昵称' }
            ],
            platform: 'kuaishou'
        },
        {
            id: 'wechat_reverse_query',
            name: '微信反查',
            description: '通过微信账号和姓名反查身份证号码信息',
            icon: 'fab fa-weixin',
            category: 'vip',
            vip: true,
            endpoint: '/api/social_reverse/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'account', label: '微信账号', type: 'text', required: true, placeholder: '请输入微信号或昵称' }
            ],
            platform: 'wechat'
        },
        {
            id: 'marriage_history_query',
            name: '婚姻史查询',
            description: '查询个人婚姻登记历史，包括结婚、离婚记录等',
            icon: 'fas fa-heart',
            category: 'vip',
            vip: true,
            endpoint: '/api/marriage_history/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ]
        },
        {
            id: 'family_query',
            name: '文字全户查询',
            description: '查询家庭成员详细信息，包括户主及所有家庭成员资料',
            icon: 'fas fa-users',
            category: 'vip',
            vip: true,
            endpoint: '/api/family_query/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入户主或家庭成员姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ]
        },
        {
            id: 'region_name_hunt',
            name: '地区姓名猎魔',
            description: '根据姓名和地区查询相关人员详细信息，包括身份证、电话、地址等',
            icon: 'fas fa-search-location',
            category: 'vip',
            vip: true,
            endpoint: '/api/region_name_hunt/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入要查询的姓名' },
                { name: 'region', label: '地区', type: 'text', required: true, placeholder: '请输入省份或城市，如：广东、北京' }
            ]
        },
        {
            id: 'realtime_location',
            name: '实时定位',
            description: '根据手机号查询实时位置信息，包括GPS坐标、详细地址、信号强度等',
            icon: 'fas fa-map-marker-alt',
            category: 'vip',
            vip: true,
            endpoint: '/api/realtime_location/index.php',
            fields: [
                { name: 'phone', label: '手机号', type: 'text', required: true, placeholder: '请输入11位手机号码' }
            ]
        },
        {
            id: 'two_factor_verify',
            name: '二要素核验',
            description: '核验姓名和身份证是否匹配，对接权威数据源，24小时人工轮班检测',
            icon: 'fas fa-user-check',
            category: 'vip',
            vip: true,
            endpoint: '/api/two_factor_verify/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ]
        },
        {
            id: 'white_bg_household',
            name: '白底个户',
            description: '生成白底样式的个人户籍信息图片，适用于各种正式场合使用',
            icon: 'fas fa-file-image',
            category: 'vip',
            vip: true,
            endpoint: '/api/white_bg_household/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ]
        },
        {
            id: 'archive_household',
            name: '档案个户',
            description: '生成档案样式的个人户籍信息图片，专业美观',
            icon: 'fas fa-folder-open',
            category: 'vip',
            vip: true,
            endpoint: '/api/archive_household/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '身份证号', type: 'text', required: true, placeholder: '请输入18位身份证号' }
            ]
        },
        {
            id: 'smart_owner_query',
            name: '智慧机主查询',
            description: '根据手机号查询机主详细信息，包括姓名、身份证、地址等',
            icon: 'fas fa-mobile-alt',
            category: 'vip',
            vip: true,
            endpoint: '/api/smart_owner_query/index.php',
            fields: [
                { name: 'phone', label: '手机号', type: 'text', required: true, placeholder: '请输入11位手机号码' }
            ]
        },
        {
            id: 'idcard_complete',
            name: '身份证库补齐',
            description: '根据姓名和部分身份证号补齐完整的身份证信息，支持多个x占位符的模糊匹配',
            icon: 'fas fa-id-card',
            category: 'vip',
            vip: true,
            endpoint: '/api/idcard_complete/index.php',
            fields: [
                { name: 'name', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'idcard', label: '部分身份证号', type: 'text', required: true, placeholder: '请输入部分身份证号，支持多个x，如：452522xxxxxxxxxx' }
            ]
        },
        {
            id: 'phone_status_check',
            name: '手机号状态检测',
            description: '检测手机号的状态信息，包括是否为空号、开通时间等详细信息',
            icon: 'fas fa-signal',
            category: 'vip',
            vip: true,
            endpoint: '/api/phone_status_check/index.php',
            fields: [
                { name: 'phone', label: '手机号', type: 'text', required: true, placeholder: '请输入11位手机号码' }
            ]
        },
        {
            id: 'kapao_listen',
            name: '卡泡聆听',
            description: '获取最新的音频文件，支持在线播放和下载功能',
            icon: 'fas fa-headphones',
            category: 'vip',
            vip: true,
            endpoint: '/api/kapao_listen/index.php',
            fields: []
        },
        {
            id: 'comprehensive_social_query',
            name: '综合社工查询',
            description: '综合查询各种社工信息，支持手机号、身份证、姓名等多种查询方式',
            icon: 'fas fa-search-plus',
            category: 'vip',
            vip: true,
            endpoint: '/api/comprehensive_social_query/index.php',
            fields: [
                { name: 'msg', label: '查询信息', type: 'text', required: true, placeholder: '请输入手机号、身份证号、姓名等信息' }
            ]
        },
        {
            id: 'influencer_hunter',
            name: '网红信息猎魔',
            description: '查询网红相关信息，支持昵称、真名等关键词搜索',
            icon: 'fas fa-star',
            category: 'vip',
            vip: true,
            endpoint: '/api/influencer_hunter/index.php',
            fields: [
                { name: 'msg', label: '查询关键词', type: 'text', required: true, placeholder: '请输入网红昵称、真名或关键词' }
            ]
        },
        {
            id: 'criminal_investigation',
            name: '刑事侦查调档',
            description: '生成刑事侦查相关文档',
            icon: 'fas fa-gavel',
            category: 'vip',
            vip: true,
            endpoint: '/api/criminal_investigation/index.php',
            fields: [
                { name: 'xm', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'hm', label: '身份证号码', type: 'text', required: true, placeholder: '请输入18位身份证号码' },
                {
                    name: 'ajxz',
                    label: '案件性质',
                    type: 'select',
                    required: true,
                    options: [
                        { value: '1', text: '轻微犯罪' },
                        { value: '2', text: '普通犯罪' },
                        { value: '3', text: '重大犯罪' },
                        { value: '4', text: '特别重大犯罪' }
                    ]
                },
                {
                    name: 'ajlb',
                    label: '案件类别',
                    type: 'select',
                    required: true,
                    options: [
                        { value: '1', text: '盗窃案' },
                        { value: '2', text: '暴力案' }
                    ]
                },
                { name: 'danwei', label: '工作单位', type: 'text', required: true, placeholder: '请输入工作单位' },
                { name: 'whcd', label: '文化程度', type: 'text', required: true, placeholder: '请输入文化程度，如：大学本科' }
            ]
        },
        {
            id: 'idcard_front_back',
            name: '身份证正反面查询',
            description: '生成身份证正反面图片',
            icon: 'fas fa-id-card-alt',
            category: 'vip',
            vip: true,
            endpoint: '/api/idcard_front_back/index.php',
            fields: [
                { name: 'xm', label: '姓名', type: 'text', required: true, placeholder: '请输入真实姓名' },
                { name: 'hm', label: '身份证号码', type: 'text', required: true, placeholder: '请输入18位身份证号码' }
            ]
        },
        {
            id: 'demon_mirror',
            name: '新版照妖镜',
            description: '创建临时空间获取照妖链接，或查看已拍摄的照片，支持远程拍照功能',
            icon: 'fas fa-camera',
            category: 'vip',
            vip: true,
            endpoint: '/api/demon_mirror/index.php',
            fields: [
                {
                    name: 'action',
                    label: '操作类型',
                    type: 'select',
                    required: true,
                    options: [
                        { value: 'create', text: '创建临时空间（生成照妖链接）' },
                        { value: 'view', text: '查看拍摄图片' }
                    ]
                },
                { name: 'pw', label: '查看密码', type: 'text', required: false, placeholder: '仅查看操作时需要填写，创建时会自动生成' }
            ]
        },
        {
            id: 'qq_phone_query',
            name: 'QQ绑定Phone查询',
            description: '根据QQ号查询绑定的手机号信息，支持查询QQ关联的手机号码',
            icon: 'fab fa-qq',
            category: 'vip',
            vip: true,
            endpoint: '/api/qq_phone_query/index.php',
            fields: [
                { name: 'qq', label: 'QQ号', type: 'text', required: true, placeholder: '请输入5-11位QQ号码' }
            ]
        }
    ],
    
    // 本地存储键名
    STORAGE_KEYS: {
        USER_TOKEN: 'idatas_user_token',
        USER_INFO: 'idatas_user_info',
        LOGIN_TIME: 'idatas_login_time',
        SEARCH_HISTORY: 'idatas_search_history',
        TOOL_FAVORITES: 'idatas_tool_favorites'
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        NETWORK_ERROR: '网络连接失败，请检查网络设置',
        SERVER_ERROR: '服务器错误，请稍后重试',
        AUTH_FAILED: '认证失败，请重新登录',
        INVALID_TOKEN: '无效的用户令牌',
        PERMISSION_DENIED: '权限不足，请升级VIP',
        VALIDATION_ERROR: '输入数据验证失败',
        UNKNOWN_ERROR: '未知错误，请联系客服'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        LOGIN_SUCCESS: '登录成功',
        REGISTER_SUCCESS: '注册成功',
        LOGOUT_SUCCESS: '退出成功',
        QUERY_SUCCESS: '查询成功',
        UPDATE_SUCCESS: '更新成功'
    }
};

// 工具类函数
const Utils = {
    // 生成18位唯一Token
    generateToken: function() {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let token = '';
        for (let i = 0; i < 18; i++) {
            token += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return token;
    },
    
    // 验证身份证号格式
    validateIdCard: function(idcard) {
        const pattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        return pattern.test(idcard);
    },
    
    // 验证手机号格式
    validatePhone: function(phone) {
        const pattern = /^1[3-9]\d{9}$/;
        return pattern.test(phone);
    },

    // 验证QQ号格式
    validateQQ: function(qq) {
        const pattern = /^\d{5,11}$/;
        return pattern.test(qq);
    },

    // 格式化日期
    formatDate: function(date) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 深拷贝
    deepClone: function(obj) {
        return JSON.parse(JSON.stringify(obj));
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, Utils };
}
