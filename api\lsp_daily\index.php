<?php
/**
 * LSP每日自律API
 * 免费功能，随机获取自律视频
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 支持GET和POST请求
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? 'get_video';
    } else {
        $action = $input['action'] ?? 'get_video';
    }
    
    // 验证操作类型
    if (!in_array($action, ['get_video', 'get_list'])) {
        echo json_encode([
            'code' => 400,
            'message' => '操作类型错误，只支持get_video或get_list'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 读取视频数据
    $result = getVideoData($action);
    
    if ($result['success']) {
        if ($action === 'get_video') {
            echo json_encode([
                'code' => 200,
                'message' => '获取自律视频成功',
                'action' => 'get_video',
                'video' => $result['video'],
                'total_count' => $result['total_count']
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        } else {
            echo json_encode([
                'code' => 200,
                'message' => '获取视频列表成功',
                'action' => 'get_list',
                'videos' => $result['videos'],
                'total_count' => $result['total_count']
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
    } else {
        echo json_encode([
            'code' => 500,
            'message' => '获取视频失败：' . $result['error']
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '系统错误：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取视频数据
 */
function getVideoData($action) {
    try {
        $jsonFile = __DIR__ . '/../zilv/shuju.json';
        
        if (!file_exists($jsonFile)) {
            return ['success' => false, 'error' => '视频数据文件不存在'];
        }
        
        $jsonContent = file_get_contents($jsonFile);
        if ($jsonContent === false) {
            return ['success' => false, 'error' => '无法读取视频数据文件'];
        }
        
        $videos = json_decode($jsonContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['success' => false, 'error' => '视频数据格式错误'];
        }
        
        if (!is_array($videos) || empty($videos)) {
            return ['success' => false, 'error' => '视频列表为空'];
        }
        
        $totalCount = count($videos);
        
        if ($action === 'get_video') {
            // 随机选择一个视频
            $randomIndex = rand(0, $totalCount - 1);
            $selectedVideo = $videos[$randomIndex];
            
            // 添加额外信息
            $selectedVideo['index'] = $randomIndex + 1;
            $selectedVideo['selected_at'] = date('Y-m-d H:i:s');
            
            return [
                'success' => true,
                'video' => $selectedVideo,
                'total_count' => $totalCount
            ];
        } else {
            // 返回所有视频列表
            return [
                'success' => true,
                'videos' => $videos,
                'total_count' => $totalCount
            ];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 验证M3U8链接
 */
function validateM3U8($url) {
    $headers = @get_headers($url, 1);
    if ($headers && strpos($headers[0], '200') !== false) {
        return true;
    }
    return false;
}

/**
 * 获取视频时长（如果可能）
 */
function getVideoDuration($m3u8Url) {
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (compatible; LSP-Daily/1.0)'
            ]
        ]);
        
        $content = @file_get_contents($m3u8Url, false, $context);
        if ($content === false) {
            return null;
        }
        
        // 简单解析M3U8文件获取时长
        $lines = explode("\n", $content);
        $totalDuration = 0;
        
        foreach ($lines as $line) {
            if (strpos($line, '#EXTINF:') === 0) {
                $duration = floatval(str_replace('#EXTINF:', '', explode(',', $line)[0]));
                $totalDuration += $duration;
            }
        }
        
        return $totalDuration > 0 ? $totalDuration : null;
        
    } catch (Exception $e) {
        return null;
    }
}
?>
